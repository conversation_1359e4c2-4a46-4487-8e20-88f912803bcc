import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  output: 'standalone',
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors (only for deployment, not recommended for development)
    ignoreBuildErrors: true,
  },
  experimental: {
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
      {
        protocol: 'http',
        hostname: '**',
      },
    ],
  },
  // Webpack config disabled when using Turbopack
  // Use Turbopack's built-in SVG support instead
  ...(process.env.NODE_ENV === 'development' 
    ? {} // Turbopack handles SVG natively
    : {
        webpack: (config) => {
          config.module.rules.push({
            test: /\.svg$/,
            use: ['@svgr/webpack'],
          });
          return config;
        },
      }),
  env: {
    WEBUI_API_BASE_URL: process.env.WEBUI_API_BASE_URL || '/api/v1',
  },
};

export default nextConfig;
