'use client';

import React from 'react';
import ReactMarkdown from 'react-markdown';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import { cn } from '@/lib/utils';
import 'katex/dist/katex.min.css';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

export const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  className
}) => {
  return (
    <div
      className={cn(
        'prose prose-gray dark:prose-invert max-w-none prose-sm',
        // Override prose styles for chat bubbles
        '[&>*:first-child]:mt-0 [&>*:last-child]:mb-0',
        // Adjust spacing for chat context
        '[&>p]:mb-2 [&>h1]:mb-2 [&>h2]:mb-2 [&>h3]:mb-2 [&>h4]:mb-2 [&>h5]:mb-1 [&>h6]:mb-1',
        '[&>ul]:mb-2 [&>ol]:mb-2 [&>blockquote]:mb-2',
        className
      )}
    >
      <ReactMarkdown
        remarkPlugins={[remarkGfm, remarkMath]}
        rehypePlugins={[rehypeKatex]}
      components={{
        // Code block rendering with copy functionality
        code(props) {
          const { children, className, node, ...rest } = props;
          const match = /language-(\w+)/.exec(className || '');
          const language = match ? match[1] : '';
          const codeString = String(children).replace(/\n$/, '');
          
          const copyToClipboard = async () => {
            try {
              await navigator.clipboard.writeText(codeString);
              // TODO: Add toast notification for copy success
            } catch (err) {
              console.error('Failed to copy code:', err);
            }
          };
          
          return match ? (
            <div className="my-3 rounded-lg overflow-hidden relative group">
              {/* Copy button */}
              <button
                onClick={copyToClipboard}
                className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-gray-600 hover:bg-gray-500 text-white text-xs px-2 py-1 rounded"
                title="Copy code"
              >
                Copy
              </button>
              {/* Language label */}
              {language && (
                <div className="absolute top-2 left-2 z-10 bg-gray-600 text-white text-xs px-2 py-1 rounded">
                  {language}
                </div>
              )}
              <SyntaxHighlighter
                {...rest}
                style={oneDark}
                language={language}
                PreTag="div"
                className="!mt-0 !mb-0 text-sm"
                showLineNumbers={codeString.split('\n').length > 5}
                customStyle={{
                  padding: '1rem',
                  margin: 0,
                  fontSize: '0.875rem',
                  lineHeight: '1.5'
                }}
              >
                {codeString}
              </SyntaxHighlighter>
            </div>
          ) : (
            <code
              {...rest}
              className="relative rounded px-[0.3rem] py-[0.2rem] font-mono text-sm bg-gray-200/50 dark:bg-gray-700/50 text-gray-800 dark:text-gray-200 break-words"
            >
              {children}
            </code>
          );
        },
        // Table rendering
        table(props) {
          const { children } = props;
          return (
            <div className="my-6 w-full overflow-y-auto">
              <table className="w-full overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700">
                {children}
              </table>
            </div>
          );
        },
        thead(props) {
          const { children } = props;
          return (
            <thead className="bg-gray-50 dark:bg-gray-800">
              {children}
            </thead>
          );
        },
        th(props) {
          const { children } = props;
          return (
            <th className="border-b border-gray-200 dark:border-gray-700 px-4 py-2 text-left font-medium text-gray-900 dark:text-gray-100">
              {children}
            </th>
          );
        },
        td(props) {
          const { children } = props;
          return (
            <td className="border-b border-gray-200 dark:border-gray-700 px-4 py-2 text-gray-700 dark:text-gray-300">
              {children}
            </td>
          );
        },
        // Link rendering
        a(props) {
          const { href, children } = props;
          return (
            <a
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 dark:text-blue-400 hover:underline"
            >
              {children}
            </a>
          );
        },
        // Blockquote rendering
        blockquote(props) {
          const { children } = props;
          return (
            <blockquote className="border-l-4 border-gray-300 dark:border-gray-700 pl-4 italic text-gray-700 dark:text-gray-300">
              {children}
            </blockquote>
          );
        },
        // List rendering with task list support
        ul(props) {
          const { children } = props;
          return (
            <ul className="list-disc pl-6 space-y-1 [&>li]:marker:text-gray-400 dark:[&>li]:marker:text-gray-500">
              {children}
            </ul>
          );
        },
        ol(props) {
          const { children } = props;
          return (
            <ol className="list-decimal pl-6 space-y-1 [&>li]:marker:text-gray-400 dark:[&>li]:marker:text-gray-500">
              {children}
            </ol>
          );
        },
        li(props) {
          const { children, className } = props;
          const isTaskItem = className?.includes('task-list-item');
          
          if (isTaskItem) {
            return (
              <li className="flex items-start space-x-2 list-none">
                {children}
              </li>
            );
          }
          
          return (
            <li className="text-gray-700 dark:text-gray-300">
              {children}
            </li>
          );
        },
        // Task list checkbox rendering
        input(props) {
          const { checked, disabled, type } = props;
          
          if (type === 'checkbox') {
            return (
              <input
                type="checkbox"
                checked={checked}
                disabled={disabled}
                className="w-4 h-4 mr-2 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                readOnly
              />
            );
          }
          
          return <input {...props} />;
        },
        // Heading rendering
        h1(props) {
          const { children } = props;
          return (
            <h1 className="text-2xl font-bold mb-4 text-gray-900 dark:text-gray-100">
              {children}
            </h1>
          );
        },
        h2(props) {
          const { children } = props;
          return (
            <h2 className="text-xl font-bold mb-3 text-gray-900 dark:text-gray-100">
              {children}
            </h2>
          );
        },
        h3(props) {
          const { children } = props;
          return (
            <h3 className="text-lg font-bold mb-2 text-gray-900 dark:text-gray-100">
              {children}
            </h3>
          );
        },
        h4(props) {
          const { children } = props;
          return (
            <h4 className="text-base font-bold mb-2 text-gray-900 dark:text-gray-100">
              {children}
            </h4>
          );
        },
        h5(props) {
          const { children } = props;
          return (
            <h5 className="text-sm font-bold mb-1 text-gray-900 dark:text-gray-100">
              {children}
            </h5>
          );
        },
        h6(props) {
          const { children } = props;
          return (
            <h6 className="text-xs font-bold mb-1 text-gray-900 dark:text-gray-100">
              {children}
            </h6>
          );
        },
        // Paragraph rendering with proper spacing
        p(props) {
          const { children } = props;
          return (
            <p className="leading-relaxed whitespace-pre-wrap">
              {children}
            </p>
          );
        },
        // Hard break rendering
        br() {
          return <br />;
        },
        // Strong/bold rendering
        strong(props) {
          const { children } = props;
          return (
            <strong className="font-bold text-gray-900 dark:text-gray-100">
              {children}
            </strong>
          );
        },
        // Emphasis/italic rendering
        em(props) {
          const { children } = props;
          return (
            <em className="italic text-gray-800 dark:text-gray-200">
              {children}
            </em>
          );
        },
        // Horizontal rule
        hr() {
          return (
            <hr className="my-4 border-gray-300 dark:border-gray-700" />
          );
        },
        // Image rendering
        img(props) {
          const { src, alt, title } = props;
          return (
            <div className="my-4">
              <img
                src={src}
                alt={alt || ''}
                title={title}
                className="max-w-full h-auto rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm"
                loading="lazy"
              />
              {alt && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-2 italic text-center">
                  {alt}
                </p>
              )}
            </div>
          );
        }
      }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};