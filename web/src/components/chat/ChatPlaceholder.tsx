'use client';

import React, { useState, useEffect } from 'react';
import { useAuthStore, useAppStore } from '@/lib/stores';
import { Tooltip } from '@/components/common';
import { MessageInput } from './MessageInput';
import { cn } from '@/lib/utils';


// Icon components
const EyeSlashIcon = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={2.5}
    stroke="currentColor"
    className={cn("w-5 h-5", className)}
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 1-4.243-4.243m4.242 4.242L9.88 9.88"
    />
  </svg>
);



interface ChatPlaceholderProps {
  transparentBackground?: boolean;
  selectedModels: string[];
  prompt: string;
  onPromptChange: (prompt: string) => void;
  onSubmit: (data: any) => void;
  temporaryChatEnabled?: boolean;
  className?: string;
}

export const ChatPlaceholder: React.FC<ChatPlaceholderProps> = ({
  transparentBackground = false,
  selectedModels,
  prompt,
  onPromptChange,
  onSubmit,
  temporaryChatEnabled = false,
  className
}) => {
  const { user } = useAuthStore();
  const { config, models } = useAppStore();
  const [selectedModelIdx, setSelectedModelIdx] = useState(0);

  // Get selected model objects
  const selectedModelObjects = selectedModels.map(id => 
    models.find(m => m.id === id)
  ).filter(Boolean);

  useEffect(() => {
    if (selectedModels.length > 0) {
      setSelectedModelIdx(selectedModelObjects.length - 1);
    }
  }, [selectedModels, selectedModelObjects.length]);

  const currentModel = selectedModelObjects[selectedModelIdx];

  return (
    <div className={cn("w-full h-full flex flex-col items-center justify-center px-4", className)}>
      {/* Temporary chat indicator */}
      {temporaryChatEnabled && (
        <Tooltip
          content="This chat won't appear in history and your messages will not be saved."
          className="w-full flex justify-center mb-4"
          placement="top"
        >
          <div className="flex items-center gap-2 text-gray-500 font-medium text-lg my-2 w-fit">
            <EyeSlashIcon className="size-5" />
            Temporary Chat
          </div>
        </Tooltip>
      )}


      {/* Model info */}
      {currentModel?.info?.meta?.description && (
        <div className="mt-8 max-w-2xl text-center">
          <Tooltip
            content={currentModel.info.meta.description}
            placement="top"
          >
            <div className="text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
              {currentModel.info.meta.description}
            </div>
          </Tooltip>
        </div>
      )}
    </div>
  );
};
