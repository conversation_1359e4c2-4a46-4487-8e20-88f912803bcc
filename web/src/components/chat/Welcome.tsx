import React from 'react';
import { cn } from '@/lib/utils';

interface WelcomeProps {
  userName?: string;
  className?: string;
}

export const Welcome: React.FC<WelcomeProps> = ({
  userName = '',
  className
}) => {
  return (
    <div className={cn("w-full max-w-2xl mb-8 flex flex-col items-start gap-2", className)}>
      {/* Greeting with background - exact Figma colors and layout */}
      <div className="bg-[#E8E9FF] rounded-xl px-3 py-2 flex items-center gap-2">
        <span className="text-[#625DEC] font-semibold text-2xl md:text-3xl lg:text-[38px] leading-[1.2] font-['Geist',sans-serif]">
          Hey, {userName}!
        </span>
        <span className="text-2xl md:text-3xl lg:text-[38px]">👋</span>
      </div>

      {/* Question text - exact Figma colors */}
      <div className="px-2">
        <span className="text-[#8384A3] font-semibold text-2xl md:text-3xl lg:text-[38px] leading-[1.2] font-['Geist',sans-serif]">
          What can I help you with?
        </span>
      </div>
    </div>
  );
};
