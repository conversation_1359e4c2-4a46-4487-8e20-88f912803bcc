'use client';

import React, { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface LanguageMenuProps {
  isOpen: boolean;
  onClose: () => void;
  onLanguageSelect: (language: string) => void;
  selectedLanguage?: string;
  className?: string;
  buttonRef?: React.RefObject<HTMLButtonElement | null>;
}

const languages = [
  { code: 'zh', name: 'Chinese' },
  { code: 'en', name: 'English' },
  { code: 'ar', name: 'Arabic' },
  { code: 'fr', name: 'French' },
  { code: 'es', name: 'Spanish' }
];

export const LanguageMenu: React.FC<LanguageMenuProps> = ({
  isOpen,
  onClose,
  onLanguageSelect,
  selectedLanguage = 'en',
  className,
  buttonRef
}) => {
  const [isAutoMode, setIsAutoMode] = useState(true);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen && buttonRef?.current) {
      const buttonRect = buttonRef.current.getBoundingClientRect();
      setPosition({
        top: buttonRect.bottom + 8, // 8px gap below button
        left: buttonRect.left
      });
    }
  }, [isOpen, buttonRef]);

  if (!isOpen) return null;

  const handleLanguageClick = (languageCode: string) => {
    onLanguageSelect(languageCode);
    onClose();
  };

  const handleToggleAuto = () => {
    setIsAutoMode(!isAutoMode);
  };

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 z-40"
        onClick={onClose}
      />
      
      {/* Menu */}
      <div
        ref={menuRef}
        className={cn(
          "fixed z-50 bg-white rounded-[12px] border border-[#E9E9EA] min-w-[200px]",
          "shadow-[0px_10px_10px_-5px_rgba(0,0,0,0.02),0px_20px_25px_-5px_rgba(0,0,0,0.02)]",
          "flex flex-col gap-1 p-1",
          className
        )}
        style={{
          top: position.top,
          left: position.left
        }}
      >
        {/* Auto Toggle */}
        <div className="flex items-center justify-between px-2 py-2 rounded-lg">
          <div className="flex flex-col justify-center">
            <span className="text-sm font-normal text-[#2E2F41] leading-[1.5em]">Auto</span>
          </div>

          {/* Toggle Switch */}
          <div
            className="relative w-10 h-5 cursor-pointer"
            onClick={handleToggleAuto}
          >
            <div
              className={cn(
                "absolute inset-0 rounded-[10px] transition-colors duration-200",
                isAutoMode ? "bg-green-500" : "bg-[#DFDFE6]"
              )}
            />
            <div
              className={cn(
                "absolute top-0.5 w-4 h-4 bg-white rounded-full transition-transform duration-200 shadow-sm",
                isAutoMode ? "translate-x-5" : "translate-x-0.5"
              )}
            />
          </div>
        </div>

        {/* Divider */}
        <div className="px-2">
          <div className="h-px bg-[#F3F3F6]" />
        </div>

        {/* Language Options */}
        <div className="flex flex-col">
          {languages.map((language) => (
            <div
              key={language.code}
              className={cn(
                "flex items-center justify-between px-2 py-2 rounded-lg cursor-pointer transition-colors duration-200",
                selectedLanguage === language.code
                  ? "bg-[#F7F8FA]"
                  : "hover:bg-[#F7F8FA]"
              )}
              onClick={() => handleLanguageClick(language.code)}
            >
              <span className="text-sm font-normal text-[#2E2F41] leading-[1.5em]">
                {language.name}
              </span>

              {/* Selection Indicator */}
              <div className="w-4 h-4 flex items-center justify-center">
                {selectedLanguage === language.code && (
                  <div className="w-2 h-2 bg-[#DFDFE6] rounded-sm" />
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  );
};
