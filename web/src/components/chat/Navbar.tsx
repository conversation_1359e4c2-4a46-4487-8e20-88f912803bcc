'use client';

import React, { useState, forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { useAuthStore } from '@/stores/auth';
import { useSettingsStore } from '@/stores/settings';
import { useUIStore } from '@/stores/ui';
import { useChatStore } from '@/stores/chat';
import { useConfigStore } from '@/stores/config';
import { useBannersStore } from '@/stores/banners';
import { ModelSelector } from './ModelSelector';
import { ShareChatModal } from './ShareChatModal';
import { Menu } from '../layout/Navbar/Menu';
import { UserMenu } from '../layout/Sidebar/UserMenu';
import { Banner } from '@/components/common/Banner';
import { Button } from '@/components/ui/button';
import { Tooltip } from '@/components/ui/tooltip';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Menu as MenuIcon,
  MessageSquare,
  MoreHorizontal,
  Settings,
  PenTool
} from 'lucide-react';

interface NavbarProps {
  chatId?: string;
  title?: string;
  shareEnabled?: boolean;
  chat?: any;
  history?: any;
  selectedModels?: string[];
  onModelsChange?: (models: string[]) => void;
  showModelSelector?: boolean;
  onNewChat?: () => void;
  className?: string;
}

export const Navbar = forwardRef<HTMLElement, NavbarProps>(({
  chatId = '',
  title = 'Open WebUI',
  shareEnabled = false,
  chat,
  history,
  selectedModels = [''],
  onModelsChange = () => {},
  showModelSelector = true,
  onNewChat = () => {},
  className
}, ref) => {
  const { user } = useAuthStore();
  const { settings } = useSettingsStore();
  const { 
    mobile, 
    showSidebar, 
    setShowSidebar, 
    showControls, 
    setShowControls,
    showArchivedChats,
    setShowArchivedChats,
    temporaryChatEnabled 
  } = useUIStore();
  const { chatId: currentChatId } = useChatStore();
  const { config } = useConfigStore();
  const { banners } = useBannersStore();

  const [showShareChatModal, setShowShareChatModal] = useState(false);
  const [showDownloadChatModal, setShowDownloadChatModal] = useState(false);

  const toggleSidebar = () => {
    setShowSidebar(!showSidebar);
  };

  const handleNewChat = () => {
    onNewChat();
  };

  const handleShareChat = () => {
    setShowShareChatModal(true);
  };

  const handleDownloadChat = () => {
    setShowDownloadChatModal(true);
  };

  const handleUserMenuAction = (action: string) => {
    if (action === 'archived-chat') {
      setShowArchivedChats(true);
    }
  };

  const shouldShowBanners = () => {
    return !history?.currentId && 
           !currentChatId && 
           (banners.length > 0 || 
            config?.license_metadata?.type === 'trial' || 
            (config?.license_metadata?.seats !== null && 
             config?.user_count > config?.license_metadata?.seats));
  };

  const getDismissedBannerIds = () => {
    try {
      return JSON.parse(localStorage.getItem('dismissedBannerIds') || '[]');
    } catch {
      return [];
    }
  };

  const dismissBanner = (bannerId: string) => {
    const dismissedIds = getDismissedBannerIds();
    const newDismissedIds = [bannerId, ...dismissedIds].filter(id => 
      banners.find(b => b.id === id)
    );
    localStorage.setItem('dismissedBannerIds', JSON.stringify(newDismissedIds));
  };

  const visibleBanners = banners.filter(banner => 
    banner.dismissible ? !getDismissedBannerIds().includes(banner.id) : true
  );

  return (
    <>
      {/* Share Chat Modal */}
      <ShareChatModal
        show={showShareChatModal}
        onClose={() => setShowShareChatModal(false)}
        chatId={chatId}
      />

      <nav 
        ref={ref}
        className={cn(
          'sticky top-[10px] z-30 w-full py-1 -mb-8 flex flex-col items-center',
          className
        )}
      >
        <div className="flex items-center w-full pl-1.5 pr-1">
          {/* Background gradient */}
          <div className="bg-gradient-to-b from-white via-white to-transparent dark:from-gray-900 dark:via-gray-900 dark:to-transparent pointer-events-none absolute inset-0 -bottom-7 z-[-1]" />

          <div className="flex max-w-full w-full mx-auto px-1 pt-0.5 bg-transparent items-center">
            <div className="flex items-center w-full max-w-full">
              {/* Sidebar Toggle */}
              <div className={cn(
                'mr-3 flex flex-none items-center text-gray-600 dark:text-gray-400',
                showSidebar ? 'md:hidden' : ''
              )}>
                <Tooltip content="Toggle Sidebar">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={toggleSidebar}
                    className="h-8 w-8 p-0"
                    aria-label="Toggle Sidebar"
                  >
                    <MenuIcon className="w-4 h-4" strokeWidth={2.5} />
                  </Button>
                </Tooltip>
              </div>

              {/* Main Content */}
              <div className={cn(
                'flex-1 overflow-hidden max-w-full flex items-center gap-3',
                showSidebar && 'ml-1'
              )}>
                {/* New Chat Button (when sidebar is collapsed) */}
                {!showSidebar && (
                  <Tooltip content="New Chat">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleNewChat}
                      className="h-8 flex items-center gap-2 flex-none"
                      aria-label="New Chat"
                    >
                      <MessageSquare className="w-4 h-4" />
                      <span className="text-sm font-medium">New Chat1</span>
                    </Button>
                  </Tooltip>
                )}

                {/* Model Selector */}
                {showModelSelector && (
                  <div className="flex-1 max-w-[330px]">
                    <ModelSelector
                      selectedModels={selectedModels}
                      onModelsChange={onModelsChange}
                      showSetDefault={!shareEnabled}
                    />
                  </div>
                )}
              </div>

              {/* Right Side Actions */}
              <div className="flex flex-none items-center text-gray-600 dark:text-gray-400">
                {/* Chat Menu */}
                {shareEnabled && chat && (chat.id || temporaryChatEnabled) && (
                  <Menu
                    chat={chat}
                    shareEnabled={shareEnabled}
                    onShare={handleShareChat}
                    onDownload={handleDownloadChat}
                  >
                    <Tooltip content="Chat Options">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        aria-label="Chat Options"
                      >
                        <MoreHorizontal className="w-5 h-5" />
                      </Button>
                    </Tooltip>
                  </Menu>
                )}

                {/* User Menu */}
                {user && (
                  <UserMenu
                    role={user.role}
                    onAction={handleUserMenuAction}
                    className="max-w-[200px]"
                  >
                    <Tooltip content="User Menu">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 rounded-xl"
                        aria-label="User Menu"
                      >
                        <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
                          {user.name?.charAt(0)?.toUpperCase() || user.email?.charAt(0)?.toUpperCase() || 'U'}
                        </div>
                      </Button>
                    </Tooltip>
                  </UserMenu>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Banners */}
        {shouldShowBanners() && (
          <div className="w-full z-30 mt-5">
            <div className="flex flex-col gap-1 w-full">
              {/* Trial License Banner */}
              {config?.license_metadata?.type === 'trial' && (
                <Banner
                  type="info"
                  title="Trial License"
                  content="You are currently using a trial license. Please contact support to upgrade your license."
                />
              )}

              {/* License Seats Exceeded Banner */}
              {config?.license_metadata?.seats !== null && 
               config?.user_count > config?.license_metadata?.seats && (
                <Banner
                  type="error"
                  title="License Error"
                  content="Exceeded the number of seats in your license. Please contact support to increase the number of seats."
                />
              )}

              {/* Custom Banners */}
              {visibleBanners.map((banner) => (
                <Banner
                  key={banner.id}
                  type={banner.type}
                  title={banner.title}
                  content={banner.content}
                  dismissible={banner.dismissible}
                  onDismiss={() => dismissBanner(banner.id)}
                />
              ))}
            </div>
          </div>
        )}
      </nav>
    </>
  );
});

Navbar.displayName = 'Navbar';