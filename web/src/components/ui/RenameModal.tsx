'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Modal } from '@/components/common/Modal';

interface RenameModalProps {
  show: boolean;
  onClose: () => void;
  onConfirm: (newName: string) => void;
  currentName: string;
  title?: string;
  placeholder?: string;
}

export const RenameModal: React.FC<RenameModalProps> = ({
  show,
  onClose,
  onConfirm,
  currentName,
  title = "Rename",
  placeholder = "Enter new name"
}) => {
  const [name, setName] = useState(currentName);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Reset name when modal opens/closes or currentName changes
  useEffect(() => {
    if (show) {
      setName(currentName);
      // Focus and select text after modal animation
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
          inputRef.current.select();
        }
      }, 100);
    }
  }, [show, currentName]);

  const handleSubmit = async () => {
    const trimmedName = name.trim();
    if (!trimmedName || trimmedName === currentName) {
      onClose();
      return;
    }

    setIsSubmitting(true);
    try {
      await onConfirm(trimmedName);
      onClose();
    } catch (error) {
      console.error('Failed to rename:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSubmit();
    }
    // Note: Escape is handled by the Modal component
  };

  return (
    <Modal
      show={show}
      onClose={onClose}
      size="sm"
      className="bg-white rounded-lg"
    >
      <div className="p-6">
        <h3 className="text-lg font-semibold text-[#2E2F41] mb-4">
          {title}
        </h3>
        
        <Input
          ref={inputRef}
          value={name}
          onChange={(e) => setName(e.target.value)}
          onKeyDown={handleKeyPress}
          placeholder={placeholder}
          disabled={isSubmitting}
          className="mb-4 border border-[#E9E9EA] bg-[#F7F8FA] text-[#2E2F41] h-[48px] rounded-xl hover:bg-[#E9E9EA] focus-visible:ring-0 focus-visible:ring-offset-0 focus:outline-none"
        />
        
        <div className="flex justify-end space-x-3">
          <Button
            variant={undefined}
            onClick={onClose}
            disabled={isSubmitting}
            className="text-[#2E2F41] h-[37px] rounded-lg hover:bg-[#E9E9EA]"
          >
            Cancel
          </Button>
          <Button
            variant={undefined}
            onClick={handleSubmit}
            disabled={isSubmitting || !name.trim()}
            className="!bg-[#625DEC] !text-white h-[37px] rounded-lg !hover:bg-[#625DEC]/80"
          >
            {isSubmitting ? 'Saving...' : 'Rename'}
          </Button>
        </div>
      </div>
    </Modal>
  );
};