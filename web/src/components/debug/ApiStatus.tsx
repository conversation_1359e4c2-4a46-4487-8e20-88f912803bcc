'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { getBackendConfig, getModels } from '@/lib/api';
import { WEBUI_API_BASE_URL } from '@/lib/constants';

export const ApiStatus: React.FC = () => {
  const [status, setStatus] = useState<{
    backend: 'loading' | 'success' | 'error';
    models: 'loading' | 'success' | 'error';
    backendData?: any;
    modelsData?: any;
    errors?: { backend?: string; models?: string };
  }>({
    backend: 'loading',
    models: 'loading'
  });

  const testApis = async () => {
    setStatus({ backend: 'loading', models: 'loading' });

    // Test backend config
    try {
      const backendData = await getBackendConfig();
      setStatus(prev => ({ 
        ...prev, 
        backend: 'success', 
        backendData,
        errors: { ...prev.errors, backend: undefined }
      }));
    } catch (error) {
      setStatus(prev => ({ 
        ...prev, 
        backend: 'error',
        errors: { ...prev.errors, backend: error instanceof Error ? error.message : String(error) }
      }));
    }

    // Test models API
    try {
      const modelsData = await getModels('');
      setStatus(prev => ({ 
        ...prev, 
        models: 'success', 
        modelsData,
        errors: { ...prev.errors, models: undefined }
      }));
    } catch (error) {
      setStatus(prev => ({ 
        ...prev, 
        models: 'error',
        errors: { ...prev.errors, models: error instanceof Error ? error.message : String(error) }
      }));
    }
  };

  useEffect(() => {
    testApis();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'loading': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'loading': return '⏳';
      default: return '❓';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4">
          API 状态检查
        </h2>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h3 className="font-semibold">API Base URL</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">{WEBUI_API_BASE_URL}</p>
            </div>
          </div>

          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h3 className="font-semibold">后端配置 API</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">/api/config</p>
            </div>
            <div className="flex items-center gap-2">
              <span className={getStatusColor(status.backend)}>
                {getStatusIcon(status.backend)} {status.backend}
              </span>
            </div>
          </div>

          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div>
              <h3 className="font-semibold">模型 API</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">/api/models</p>
            </div>
            <div className="flex items-center gap-2">
              <span className={getStatusColor(status.models)}>
                {getStatusIcon(status.models)} {status.models}
              </span>
            </div>
          </div>
        </div>

        <Button onClick={testApis} className="mt-4">
          重新测试 API
        </Button>

        {status.errors?.backend && (
          <Alert className="mt-4">
            <AlertDescription>
              <strong>后端 API 错误:</strong> {status.errors.backend}
            </AlertDescription>
          </Alert>
        )}

        {status.errors?.models && (
          <Alert className="mt-4">
            <AlertDescription>
              <strong>模型 API 错误:</strong> {status.errors.models}
            </AlertDescription>
          </Alert>
        )}

        {status.modelsData && (
          <div className="mt-4 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
            <h4 className="font-semibold mb-2">可用模型 ({status.modelsData.length})</h4>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {status.modelsData.map((model: any, index: number) => (
                <div key={index} className="text-sm">
                  <span className="font-mono">{model.id}</span>
                  {model.name && model.name !== model.id && (
                    <span className="text-gray-600 dark:text-gray-400 ml-2">({model.name})</span>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};