"use client";

import React, { useState, useEffect, useCallback } from "react";
import { MessageSquare, <PERSON><PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useAppStore, useAuthStore, useChatStore } from "@/lib/stores";
import { useChats } from "@/hooks/useChats";
import { ChatItem } from "@/components/layout/sidebar/ChatItem";
// import type { ConversationEntry } from "@/lib/types"; // Unused for now
import {
  getChatById,
  updateChatById,
  deleteChatById,
  cloneChatById,
  shareChatById,
  archiveChatById,
  toggleChatPinnedById,
  getPinnedChatList,
  generateTitle,
  getAllTags
} from "@/lib/api";

interface ConversationListProps {
  currentConversationId?: string;
  onConversationSelect: (id: string) => void;
  onNewConversation: (type?: 'chat' | 'agent') => void;
  className?: string;
}

interface ChatListItem {
  id: string;
  title: string;
  updated_at: string;
  created_at: string;
  pinned?: boolean;
  archived?: boolean;
  tags?: string[];
  folder_id?: string;
  chat?: {
    messages?: Record<string, unknown>[];
    models?: string[];
  };
}

export const ConversationList: React.FC<ConversationListProps> = ({
  currentConversationId,
  onConversationSelect,
  onNewConversation,
  className
}) => {
  const { models } = useAppStore();
  const { token, isAuthenticated } = useAuthStore();
  const { chats, isLoading, loadChats } = useChats(); // Use the shared chats state
  const [pinnedChats, setPinnedChats] = useState<ChatListItem[]>([]);
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  const [shiftKey, setShiftKey] = useState(false);
  const [tags, setTags] = useState<unknown[]>([]);
  
  // Use chats from useChats hook instead of local state
  const chatList = chats;
  
  // Load pinned chats and tags (regular chats are handled by useChats hook)
  const loadAdditionalData = useCallback(async () => {
    if (!token) return;
    
    try {
      const [pinnedResponse, tagsResponse] = await Promise.all([
        getPinnedChatList(token),
        getAllTags(token)
      ]);
      
      setPinnedChats(pinnedResponse || []);
      setTags(tagsResponse || []);
    } catch (error) {
      console.error('Failed to load additional chat data:', error);
    }
  }, [token]);
  
  useEffect(() => {
    if (isAuthenticated) {
      loadAdditionalData();
    }
  }, [isAuthenticated, loadAdditionalData]);
  
  // Keyboard event handling
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      setShiftKey(e.shiftKey);
    };
    
    const handleKeyUp = (e: KeyboardEvent) => {
      setShiftKey(e.shiftKey);
    };
    
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, []);
  
  // Chat handlers
  // Helper function to validate chat ID format (should be UUID)
  const isValidChatId = useCallback((id: string) => {
    if (!id || id === 'new' || id === 'local') return false;
    // Check if it's a valid UUID format (8-4-4-4-12 characters)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    const isValid = uuidRegex.test(id);
    return isValid;
  }, []);

  const handleChatRename = useCallback(async (chatId: string, newTitle: string) => {
    if (!token || !isValidChatId(chatId)) {
      return;
    }
    
    try {
      const chat = await getChatById(token, chatId);
      if (chat) {
        const updatedChat = {
          ...chat.chat,
          title: newTitle
        };
        await updateChatById(token, chatId, updatedChat);
        // Refresh both additional data and main chat list
        await Promise.all([
          loadAdditionalData(),
          loadChats(1) // Refresh main chat list to update titles
        ]);
      }
    } catch (error) {
      console.error('Failed to rename chat:', error);
    }
  }, [token, loadAdditionalData, loadChats, isValidChatId]);
  
  const handleChatDelete = useCallback(async (chatId: string) => {
    if (!token || !isValidChatId(chatId)) {
      return;
    }
    
    try {
      await deleteChatById(token, chatId);
      // Refresh both additional data and main chat list
      await Promise.all([
        loadAdditionalData(),
        loadChats(1) // Refresh main chat list
      ]);
    } catch (error) {
      console.error('Failed to delete chat:', error);
    }
  }, [token, loadAdditionalData, loadChats, isValidChatId]);
  
  const handleChatClone = useCallback(async (chatId: string) => {
    if (!token || !isValidChatId(chatId)) {
      return;
    }
    
    try {
      const chat = chatList.find(c => c.id === chatId) || pinnedChats.find(c => c.id === chatId);
      if (chat) {
        const cloneTitle = `Clone of ${chat.title}`;
        await cloneChatById(token, chatId, cloneTitle);
        // Refresh both additional data and main chat list
        await Promise.all([
          loadAdditionalData(),
          loadChats(1) // Refresh main chat list
        ]);
      }
    } catch (error) {
      console.error('Failed to clone chat:', error);
    }
  }, [token, chatList, pinnedChats, loadAdditionalData, loadChats, isValidChatId]);
  
  const handleChatShare = useCallback(async (chatId: string) => {
    if (!token || !isValidChatId(chatId)) {
      return;
    }
    
    try {
      const sharedChat = await shareChatById(token, chatId);
      if (sharedChat) {
        // Handle share success (maybe show a modal or copy link)
      }
    } catch (error) {
      console.error('Failed to share chat:', error);
    }
  }, [token, isValidChatId]);
  
  const handleChatArchive = useCallback(async (chatId: string) => {
    if (!token || !isValidChatId(chatId)) {
      return;
    }
    
    try {
      await archiveChatById(token, chatId);
      // Refresh both additional data and main chat list
      await Promise.all([
        loadAdditionalData(),
        loadChats(1) // Refresh main chat list
      ]);
    } catch (error) {
      console.error('Failed to archive chat:', error);
    }
  }, [token, loadAdditionalData, loadChats, isValidChatId]);
  
  const handleChatPin = useCallback(async (chatId: string) => {
    if (!token) return;
    
    try {
      await toggleChatPinnedById(token, chatId);
      // Refresh both additional data and main chat list
      await Promise.all([
        loadAdditionalData(),
        loadChats(1) // Refresh main chat list
      ]);
    } catch (error) {
      console.error('Failed to toggle pin status:', error);
    }
  }, [token, loadAdditionalData, loadChats]);
  
  const handleGenerateTitle = useCallback(async (chatId: string): Promise<string | null> => {
    if (!token) return null;
    
    try {
      const chat = await getChatById(token, chatId);
      if (chat && chat.chat?.messages && models.length > 0) {
        const messages = Object.values(chat.chat.messages)
          .sort((a: any, b: any) => {
            const aTime = a.timestamp || 0;
            const bTime = b.timestamp || 0;
            return aTime - bTime; // Sort chronologically
          })
          .map((msg: any) => ({
            role: msg.role,
            content: msg.content
          }));
        
        const model = chat.chat.models?.[0] || models[0]?.id;
        if (model) {
          return await generateTitle(token, model, messages);
        }
      }
      return null;
    } catch (error) {
      console.error('Failed to generate title:', error);
      return null;
    }
  }, [token, models]);
  
  const handleChatSelect = useCallback((chatId: string) => {
    setSelectedChat(chatId);
    onConversationSelect(chatId);
  }, [onConversationSelect]);
  
  const handleChatUnselect = useCallback(() => {
    setSelectedChat(null);
  }, []);
  
  const allChats = [...pinnedChats, ...chatList];

  if (isLoading) {
    return (
      <div className={cn("flex flex-col h-full", className)}>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-gray-500 text-sm">Loading conversations...</div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("h-full overflow-y-auto", className)}>
      {allChats.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <div className="mb-4">
              <MessageSquare className="w-12 h-12 mx-auto text-gray-300" />
            </div>
            <p className="text-sm mb-4">No conversations yet</p>
            <div className="space-y-2">
              <Button
                onClick={() => onNewConversation('chat')}
                className="w-full bg-[#ffffff] hover:bg-gray-100 border border-gray-200 mb-2"
                size="sm"
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                Start Chat
              </Button>
              <Button
                onClick={() => onNewConversation('agent')}
                variant="outline"
                className="w-full"
                size="sm"
              >
                <Bot className="w-4 h-4 mr-2" />
                Start Agent Research
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-1">
            {allChats.map((chat) => (
              <ChatItem
                key={chat.id}
                chat={chat as any}
                isActive={currentConversationId === chat.id}
                isSelected={selectedChat === chat.id}
                shiftKey={shiftKey}
                onSelect={() => handleChatSelect(chat.id)}
                onUnselect={handleChatUnselect}
                onRename={(newTitle) => handleChatRename(chat.id, newTitle)}
                onDelete={() => handleChatDelete(chat.id)}
                onClone={() => handleChatClone(chat.id)}
                onShare={() => handleChatShare(chat.id)}
                onArchive={() => handleChatArchive(chat.id)}
                onPin={() => handleChatPin(chat.id)}
                onGenerateTitle={() => handleGenerateTitle(chat.id)}
                onChange={() => {
                  loadAdditionalData();
                  loadChats(1); // Also refresh main chat list
                }}
                draggable={true}
                className="mx-0"
              />
            ))}
          </div>
        )}
    </div>
  );
};