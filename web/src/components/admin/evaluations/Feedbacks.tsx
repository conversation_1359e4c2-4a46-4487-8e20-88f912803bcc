'use client';

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Spinner } from '@/components/ui/spinner';
import { useI18n } from '@/lib/i18n';
import { useAuthStore } from '@/lib/stores';
import { deleteFeedbackById, exportAllFeedbacks, type Feedback } from '@/lib/api/evaluations';
import { toast } from 'sonner';
import {
  Download,
  Upload,
  ChevronUp,
  ChevronDown,
  MoreHorizontal,
  Trash2
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface FeedbacksProps {
  feedbacks?: Feedback[];
  stats?: any;
  className?: string;
}

export const Feedbacks: React.FC<FeedbacksProps> = ({
  feedbacks = [],
  stats = {},
  className
}) => {
  const { t } = useI18n();
  const { token } = useAuthStore();

  const [page, setPage] = useState(1);
  const [orderBy, setOrderBy] = useState<string>('updated_at');
  const [direction, setDirection] = useState<'asc' | 'desc'>('desc');
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [selectedFeedback, setSelectedFeedback] = useState<Feedback | null>(null);

  const setSortKey = (key: string) => {
    if (orderBy === key) {
      setDirection(direction === 'asc' ? 'desc' : 'asc');
    } else {
      setOrderBy(key);
      if (key === 'user' || key === 'model_id') {
        setDirection('asc');
      } else {
        setDirection('desc');
      }
    }
    setPage(1);
  };

  const sortedFeedbacks = [...feedbacks].sort((a, b) => {
    let aVal: any, bVal: any;

    switch (orderBy) {
      case 'user':
        aVal = a.user?.name || '';
        bVal = b.user?.name || '';
        return direction === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
      case 'model_id':
        aVal = a.data.model_id || '';
        bVal = b.data.model_id || '';
        return direction === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
      case 'rating':
        aVal = a.data.rating;
        bVal = b.data.rating;
        return direction === 'asc' ? aVal - bVal : bVal - aVal;
      case 'updated_at':
        aVal = a.updated_at;
        bVal = b.updated_at;
        return direction === 'asc' ? aVal - bVal : bVal - aVal;
      default:
        return 0;
    }
  });

  const itemsPerPage = 10;
  const paginatedFeedbacks = sortedFeedbacks.slice((page - 1) * itemsPerPage, page * itemsPerPage);

  const openFeedbackModal = (feedback: Feedback) => {
    setSelectedFeedback(feedback);
    setShowFeedbackModal(true);
  };

  const closeFeedbackModal = () => {
    setSelectedFeedback(null);
    setShowFeedbackModal(false);
  };

  const deleteFeedbackHandler = async (feedbackId: string) => {
    if (!token) return;

    try {
      await deleteFeedbackById(token, feedbackId);
      toast.success(t('Feedback deleted successfully'));
      // In a real app, this would trigger a refetch of feedbacks
      // For now, we'll just close the modal if it's the selected feedback
      if (selectedFeedback?.id === feedbackId) {
        closeFeedbackModal();
      }
    } catch (error) {
      console.error('Failed to delete feedback:', error);
      toast.error(t('Failed to delete feedback'));
    }
  };

  const exportFeedbacksHandler = async () => {
    if (!token) return;

    try {
      const feedbacksData = await exportAllFeedbacks(token);
      const blob = new Blob([JSON.stringify(feedbacksData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `feedbacks-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success(t('Feedbacks exported successfully'));
    } catch (error) {
      console.error('Failed to export feedbacks:', error);
      toast.error(t('Failed to export feedbacks'));
    }
  };

  return (
    <div className={cn("mt-0.5 mb-2 gap-1 flex flex-col", className)}>
      {/* Header */}
      <div className="flex md:self-center text-lg font-medium px-0.5 shrink-0 items-center mb-4">
        <div className="gap-1">
          {t('Feedbacks')}
        </div>
        <div className="flex self-center w-[1px] h-6 mx-2.5 bg-gray-50 dark:bg-gray-850" />
        <span className="text-lg font-medium text-gray-500 dark:text-gray-300 mr-1.5">
          {feedbacks.length}
        </span>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={exportFeedbacksHandler}
            className="flex items-center gap-2"
          >
            <Download className="w-4 h-4" />
            {t('Export')}
          </Button>
        </div>

        <div className="text-xs text-gray-500 dark:text-gray-400">
          {t('Help us create the best community leaderboard by sharing your feedback history!')}
        </div>
      </div>

      {/* Feedbacks Table */}
      <div className="scrollbar-hidden relative whitespace-nowrap overflow-x-auto max-w-full rounded-sm pt-0.5">
        {feedbacks.length === 0 ? (
          <div className="text-center text-xs text-gray-500 dark:text-gray-400 py-8">
            {t('No feedbacks found')}
          </div>
        ) : (
          <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400 table-auto max-w-full rounded">
            <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-850 dark:text-gray-400 -translate-y-0.5">
              <tr>
                <th scope="col" className="px-3 py-1.5 text-center cursor-pointer select-none w-0">
                  {t('User')}
                </th>
                <th
                  scope="col"
                  className="px-3 py-1.5 cursor-pointer select-none"
                  onClick={() => setSortKey('model_id')}
                >
                  <div className="flex items-center gap-1">
                    {t('Model')}
                    {orderBy === 'model_id' && (
                      direction === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-3 py-1.5 cursor-pointer select-none"
                  onClick={() => setSortKey('rating')}
                >
                  <div className="flex items-center gap-1">
                    {t('Rating')}
                    {orderBy === 'rating' && (
                      direction === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                    )}
                  </div>
                </th>
                <th scope="col" className="px-3 py-1.5">
                  {t('Comment')}
                </th>
                <th
                  scope="col"
                  className="px-3 py-1.5 cursor-pointer select-none"
                  onClick={() => setSortKey('updated_at')}
                >
                  <div className="flex items-center gap-1">
                    {t('Updated')}
                    {orderBy === 'updated_at' && (
                      direction === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                    )}
                  </div>
                </th>
                <th scope="col" className="px-3 py-1.5 text-right cursor-pointer select-none w-0">
                  {/* Actions */}
                </th>
              </tr>
            </thead>
            <tbody>
              {paginatedFeedbacks.map((feedback) => (
                <tr
                  key={feedback.id}
                  className="bg-white dark:bg-gray-900 dark:border-gray-850 text-xs cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition"
                  onClick={() => openFeedbackModal(feedback)}
                >
                  <td className="py-0.5 text-right font-semibold">
                    <div className="flex justify-center">
                      <div className="shrink-0">
                        <img
                          src={feedback.user?.profile_image_url || '/user.png'}
                          alt={feedback.user?.name || 'User'}
                          className="size-5 rounded-full object-cover shrink-0"
                        />
                      </div>
                    </div>
                  </td>
                  <td className="px-3 py-1 font-semibold">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        {feedback.data.model_id}
                      </Badge>
                    </div>
                  </td>
                  <td className="px-3 py-1 text-center font-semibold">
                    <div className="flex justify-center">
                      <Badge
                        variant={feedback.data.rating >= 4 ? "default" : feedback.data.rating <= 2 ? "destructive" : "secondary"}
                        className="text-xs"
                      >
                        {feedback.data.rating}/5
                      </Badge>
                    </div>
                  </td>
                  <td className="px-3 py-1 max-w-xs">
                    <div className="truncate text-xs">
                      {feedback.data.comment || '-'}
                    </div>
                  </td>
                  <td className="px-3 py-1 text-center font-semibold">
                    <div className="text-xs text-gray-500">
                      {new Date(feedback.updated_at * 1000).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="px-3 py-1 text-right font-semibold">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteFeedbackHandler(feedback.id);
                          }}
                          className="text-red-600 dark:text-red-400"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          {t('Delete')}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>


      {/* Pagination */}
      {feedbacks.length > itemsPerPage && (
        <div className="flex justify-center mt-4">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(Math.max(1, page - 1))}
              disabled={page === 1}
            >
              Previous
            </Button>
            <span className="text-sm text-gray-500">
              Page {page} of {Math.ceil(feedbacks.length / itemsPerPage)}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(Math.min(Math.ceil(feedbacks.length / itemsPerPage), page + 1))}
              disabled={page >= Math.ceil(feedbacks.length / itemsPerPage)}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
