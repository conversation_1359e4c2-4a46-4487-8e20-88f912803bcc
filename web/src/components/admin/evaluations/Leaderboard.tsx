'use client';

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Spinner } from '@/components/ui/spinner';
import { useI18n } from '@/lib/i18n';
import { useAppStore } from '@/lib/stores';
import type { Feedback } from '@/lib/api/evaluations';
import {
  Search,
  ChevronUp,
  ChevronDown
} from 'lucide-react';

interface ModelStats {
  rating: number;
  won: number;
  lost: number;
}

interface RankedModel {
  id: string;
  name: string;
  owned_by?: string;
  info?: {
    meta?: {
      hidden?: boolean;
    };
  };
  rating: number | string;
  stats: {
    count: number;
    won: string;
    lost: string;
  };
}

interface LeaderboardProps {
  feedbacks?: Feedback[];
  stats?: any;
  className?: string;
}

export const Leaderboard: React.FC<LeaderboardProps> = ({
  feedbacks = [],
  stats = {},
  className
}) => {
  const { t } = useI18n();
  const { models } = useAppStore();

  const [rankedModels, setRankedModels] = useState<RankedModel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [query, setQuery] = useState('');
  const [orderBy, setOrderBy] = useState<string>('rating');
  const [direction, setDirection] = useState<'asc' | 'desc'>('desc');

  useEffect(() => {
    rankHandler();
  }, [feedbacks, models]);

  const calculateModelStats = (feedbacks: Feedback[], similarities: Map<string, number> = new Map()): Map<string, ModelStats> => {
    const modelStats = new Map<string, ModelStats>();

    feedbacks.forEach((feedback) => {
      const modelId = feedback.data.model_id;
      if (!modelId) return;

      if (!modelStats.has(modelId)) {
        modelStats.set(modelId, { rating: 1000, won: 0, lost: 0 });
      }

      const stats = modelStats.get(modelId)!;

      // Simple rating calculation based on feedback rating
      if (feedback.data.rating >= 4) {
        stats.won += 1;
        stats.rating += 10;
      } else if (feedback.data.rating <= 2) {
        stats.lost += 1;
        stats.rating -= 10;
      }
    });

    return modelStats;
  };

  const rankHandler = async (similarities: Map<string, number> = new Map()) => {
    setIsLoading(true);
    try {
      const modelStats = calculateModelStats(feedbacks, similarities);

      const ranked = models
        .filter((m) => m?.owned_by !== 'arena' && (m?.info?.meta?.hidden ?? false) !== true)
        .map((model) => {
          const stats = modelStats.get(model.id);
          return {
            ...model,
            rating: stats ? Math.round(stats.rating) : '-',
            stats: {
              count: stats ? stats.won + stats.lost : 0,
              won: stats ? stats.won.toString() : '-',
              lost: stats ? stats.lost.toString() : '-'
            }
          };
        })
        .sort((a, b) => {
          if (a.rating === '-' && b.rating !== '-') return 1;
          if (b.rating === '-' && a.rating !== '-') return -1;
          if (a.rating !== '-' && b.rating !== '-') return (b.rating as number) - (a.rating as number);
          return a.name.localeCompare(b.name);
        });

      setRankedModels(ranked);
    } catch (error) {
      console.error('Failed to rank models:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const setSortKey = (key: string) => {
    if (orderBy === key) {
      setDirection(direction === 'asc' ? 'desc' : 'asc');
    } else {
      setOrderBy(key);
      setDirection(key === 'name' ? 'asc' : 'desc');
    }
  };

  const filteredModels = rankedModels.filter((model) => {
    if (!query.trim()) return true;
    return model.name.toLowerCase().includes(query.toLowerCase()) ||
           model.id.toLowerCase().includes(query.toLowerCase());
  });

  const sortedModels = [...filteredModels].sort((a, b) => {
    let aVal: any, bVal: any;

    switch (orderBy) {
      case 'name':
        aVal = a.name || '';
        bVal = b.name || '';
        return direction === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
      case 'rating':
        aVal = a.rating === '-' ? 0 : a.rating;
        bVal = b.rating === '-' ? 0 : b.rating;
        return direction === 'asc' ? aVal - bVal : bVal - aVal;
      case 'count':
        aVal = a.stats.count;
        bVal = b.stats.count;
        return direction === 'asc' ? aVal - bVal : bVal - aVal;
      default:
        return 0;
    }
  });

  return (
    <div className={cn("mt-0.5 mb-2 gap-1 flex flex-col", className)}>
      {/* Header */}
      <div className="flex md:self-center text-lg font-medium px-0.5 shrink-0 items-center mb-4">
        <div className="gap-1">
          {t('Leaderboard')}
        </div>
        <div className="flex self-center w-[1px] h-6 mx-2.5 bg-gray-50 dark:bg-gray-850" />
        <span className="text-lg font-medium text-gray-500 dark:text-gray-300 mr-1.5">
          {rankedModels.length}
        </span>
      </div>

      {/* Search */}
      <div className="flex items-center gap-2 mb-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder={t('Search models...')}
            className="pl-10"
          />
        </div>
      </div>

      {/* Leaderboard Table */}
      <div className="scrollbar-hidden relative whitespace-nowrap overflow-x-auto max-w-full rounded-sm pt-0.5">
        {isLoading && (
          <div className="absolute top-0 bottom-0 left-0 right-0 flex">
            <div className="m-auto">
              <Spinner />
            </div>
          </div>
        )}

        {sortedModels.length === 0 ? (
          <div className="text-center text-xs text-gray-500 dark:text-gray-400 py-1">
            {t('No models found')}
          </div>
        ) : (
          <table className={cn(
            "w-full text-sm text-left text-gray-500 dark:text-gray-400 table-auto max-w-full rounded",
            isLoading ? 'opacity-20' : ''
          )}>
            <thead className="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-850 dark:text-gray-400 -translate-y-0.5">
              <tr>
                <th
                  scope="col"
                  className="px-3 py-1.5 cursor-pointer select-none w-3"
                  onClick={() => setSortKey('rank')}
                >
                  #
                </th>
                <th
                  scope="col"
                  className="px-3 py-1.5 cursor-pointer select-none"
                  onClick={() => setSortKey('name')}
                >
                  <div className="flex items-center gap-1">
                    {t('Model')}
                    {orderBy === 'name' && (
                      direction === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-3 py-1.5 cursor-pointer select-none"
                  onClick={() => setSortKey('rating')}
                >
                  <div className="flex items-center gap-1">
                    {t('Rating')}
                    {orderBy === 'rating' && (
                      direction === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-3 py-1.5 cursor-pointer select-none"
                  onClick={() => setSortKey('count')}
                >
                  <div className="flex items-center gap-1">
                    {t('Battles')}
                    {orderBy === 'count' && (
                      direction === 'asc' ? <ChevronUp className="w-3 h-3" /> : <ChevronDown className="w-3 h-3" />
                    )}
                  </div>
                </th>
                <th scope="col" className="px-3 py-1.5">
                  {t('Win Rate')}
                </th>
              </tr>
            </thead>
            <tbody>
              {sortedModels.map((model, index) => (
                <tr
                  key={model.id}
                  className="bg-white dark:bg-gray-900 dark:border-gray-850 text-xs cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition"
                >
                  <td className="px-3 py-2 font-medium text-gray-900 dark:text-white">
                    {index + 1}
                  </td>
                  <td className="px-3 py-2">
                    <div className="flex items-center gap-2">
                      <div className="font-medium text-gray-900 dark:text-white">
                        {model.name}
                      </div>
                    </div>
                  </td>
                  <td className="px-3 py-2 font-medium">
                    {model.rating}
                  </td>
                  <td className="px-3 py-2">
                    {model.stats.count}
                  </td>
                  <td className="px-3 py-2">
                    {model.stats.count > 0
                      ? `${Math.round((parseInt(model.stats.won) / model.stats.count) * 100)}%`
                      : '-'
                    }
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

    </div>
  );
};
