'use client';

import React, { useState, useEffect } from 'react';
import { useAuthStore } from '@/lib/stores';
import {
  getGroups,
  createNewGroup,
  deleteGroupById,
  updateGroupById,
  type Group,
  type CreateGroupRequest
} from '@/lib/api/groups';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Users,
  Search,
  Plus,
  Edit,
  Trash2,
  MoreVertical,
  Settings,
  Shield,
  User,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { AddGroupModal } from './groups/AddGroupModal';
import { EditGroupModal } from './groups/EditGroupModal';
import { DefaultPermissionsModal } from './groups/DefaultPermissionsModal';

interface Permission {
  workspace: {
    models: boolean;
    knowledge: boolean;
    prompts: boolean;
    tools: boolean;
  };
  sharing: {
    public_models: boolean;
    public_knowledge: boolean;
    public_prompts: boolean;
    public_tools: boolean;
  };
  chat: {
    controls: boolean;
    system_prompt: boolean;
    file_upload: boolean;
    delete: boolean;
    edit: boolean;
    share: boolean;
    export: boolean;
    stt: boolean;
    tts: boolean;
    call: boolean;
    multiple_models: boolean;
    temporary: boolean;
    temporary_enforced: boolean;
  };
  features: {
    direct_tool_servers: boolean;
    web_search: boolean;
    image_generation: boolean;
    code_interpreter: boolean;
    notes: boolean;
  };
}

export default function Groups() {
  const { token } = useAuthStore();
  const [groups, setGroups] = useState<Group[]>([]);
  const [search, setSearch] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateGroupModal, setShowCreateGroupModal] = useState(false);
  const [showEditGroupModal, setShowEditGroupModal] = useState(false);
  const [showDefaultPermissionsModal, setShowDefaultPermissionsModal] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
  const [users, setUsers] = useState<any[]>([]);
  const [defaultPermissions, setDefaultPermissions] = useState<Permission>({
    workspace: {
      models: false,
      knowledge: false,
      prompts: false,
      tools: false
    },
    sharing: {
      public_models: false,
      public_knowledge: false,
      public_prompts: false,
      public_tools: false
    },
    chat: {
      controls: true,
      system_prompt: true,
      file_upload: true,
      delete: true,
      edit: true,
      share: true,
      export: true,
      stt: true,
      tts: true,
      call: true,
      multiple_models: true,
      temporary: true,
      temporary_enforced: false
    },
    features: {
      direct_tool_servers: false,
      web_search: true,
      image_generation: true,
      code_interpreter: true,
      notes: true
    }
  });

  // Load groups from API
  const loadGroups = async () => {
    if (!token) return;

    try {
      setIsLoading(true);
      const groupsData = await getGroups(token);
      setGroups(groupsData);
    } catch (error) {
      console.error('Failed to load groups:', error);
      toast.error('Failed to load groups');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateGroup = async (groupData: CreateGroupRequest) => {
    if (!token) return;

    try {
      const newGroup = await createNewGroup(token, groupData);
      setGroups([...groups, newGroup]);
      toast.success('Group created successfully');
    } catch (error) {
      console.error('Failed to create group:', error);
      toast.error('Failed to create group');
    }
  };

  // Update group
  const handleUpdateGroup = async (groupData: any) => {
    if (!token) return;

    try {
      const updatedGroup = await updateGroupById(token, groupData.id, groupData);
      setGroups(groups.map(group =>
        group.id === groupData.id ? updatedGroup : group
      ));
      toast.success('Group updated successfully');
    } catch (error) {
      console.error('Failed to update group:', error);
      toast.error('Failed to update group');
    }
  };

  const handleDeleteGroup = async (id: string) => {
    if (!token) return;

    try {
      await deleteGroupById(token, id);
      setGroups(groups.filter(g => g.id !== id));
      toast.success('Group deleted successfully');
    } catch (error) {
      console.error('Failed to delete group:', error);
      toast.error('Failed to delete group');
    }
  };

  // Handle default permissions
  const handleUpdateDefaultPermissions = async (permissions: Permission) => {
    // TODO: Implement API call to update default permissions
    setDefaultPermissions(permissions);
    toast.success('Default permissions updated');
  };

  // Handle edit group
  const handleEditGroup = (group: Group) => {
    setSelectedGroup(group);
    setShowEditGroupModal(true);
  };

  useEffect(() => {
    loadGroups();
  }, [token]);

  const filteredGroups = groups.filter((group) => {
    if (search === '') {
      return true;
    } else {
      const name = group.name.toLowerCase();
      const query = search.toLowerCase();
      return name.includes(query);
    }
  });

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const getPermissionBadgeColor = (permission: string) => {
    const colors: Record<string, string> = {
      admin: 'bg-red-100 text-red-800',
      read: 'bg-blue-100 text-blue-800',
      write: 'bg-green-100 text-green-800',
      delete: 'bg-red-100 text-red-800',
      chat: 'bg-purple-100 text-purple-800',
      advanced_features: 'bg-yellow-100 text-yellow-800',
      create_content: 'bg-indigo-100 text-indigo-800',
      share_public: 'bg-pink-100 text-pink-800',
      manage_users: 'bg-orange-100 text-orange-800'
    };
    return colors[permission] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="w-full">
      {/* Header */}
      <div className="flex justify-between items-start mb-6">
        <div className="flex-1">
          <div className="flex items-center gap-4 mb-4">
            <div className="flex items-center gap-2">
              <h1 className="text-lg font-medium">Groups</h1>
              <div className="w-px h-6 bg-gray-300 dark:bg-gray-600" />
              <Badge variant="secondary">{groups.length}</Badge>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search groups..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setShowDefaultPermissionsModal(true)}
            >
              <Settings className="w-4 h-4 mr-2" />
              Default Permissions
            </Button>
          </div>
        </div>

        <Button onClick={() => setShowCreateGroupModal(true)} className="ml-4 bg-[#ffffff] hover:bg-gray-100 border border-gray-200">
          <Plus className="w-4 h-4 mr-2" />
          Create Group
        </Button>
      </div>

      {/* Groups Content */}
      {isLoading ? (
        <div className="text-center py-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
            <span className="ml-2">Loading groups...</span>
          </div>
        </div>
      ) : filteredGroups.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-16">
          <div className="text-xl font-medium mb-2">
            Organize your users
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400 mb-6">
            Use groups to group your users and assign permissions.
          </div>
          <Button
            onClick={() => setShowCreateGroupModal(true)}
            className="flex items-center gap-2 bg-[#ffffff] hover:bg-gray-100 border border-gray-200"
          >
            <Plus className="w-4 h-4" />
            Create Group
          </Button>
        </div>
      ) : (
        <div>
          {/* Table Header */}
          <div className="flex items-center gap-3 justify-between text-xs uppercase px-1 font-bold mb-4">
            <div className="w-full">Group</div>
            <div className="w-full">Users</div>
            <div className="w-full"></div>
          </div>
          <hr className="mb-4 border-gray-200 dark:border-gray-700" />

          {/* Groups List */}
          <div className="space-y-4">
            {filteredGroups.map((group) => (
            <Card key={group.id} className="p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                    <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg text-gray-900 dark:text-white">
                      {group.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {group.description}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedGroup(group)}
                >
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </div>

              <div className="space-y-4">
                {/* User Count */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4 text-gray-500" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Members
                    </span>
                  </div>
                  <Badge variant="secondary">{group.user_count}</Badge>
                </div>

                {/* Permissions */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Shield className="w-4 h-4 text-gray-500" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Permissions
                    </span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {group.permissions.slice(0, 3).map((permission) => (
                      <Badge
                        key={permission}
                        variant="outline"
                        className={cn("text-xs", getPermissionBadgeColor(permission))}
                      >
                        {permission.replace('_', ' ')}
                      </Badge>
                    ))}
                    {group.permissions.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{group.permissions.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Created Date */}
                <div className="text-xs text-gray-500">
                  Created {formatDate(group.created_at)}
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-2 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1"
                  onClick={() => handleEditGroup(group)}
                >
                  <Edit className="w-3 h-3 mr-1" />
                  Edit
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-red-600 hover:text-red-700"
                  onClick={() => handleDeleteGroup(group.id)}
                >
                  <Trash2 className="w-3 h-3" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                >
                  <ChevronRight className="w-3 h-3" />
                </Button>
              </div>
            </Card>
            ))}
          </div>
        </div>
      )}

      {/* Modals */}
      <AddGroupModal
        open={showCreateGroupModal}
        onOpenChange={setShowCreateGroupModal}
        onSubmit={handleCreateGroup}
      />

      <EditGroupModal
        open={showEditGroupModal}
        onOpenChange={setShowEditGroupModal}
        group={selectedGroup}
        onSubmit={handleUpdateGroup}
        onDelete={handleDeleteGroup}
        users={users}
      />

      <DefaultPermissionsModal
        open={showDefaultPermissionsModal}
        onOpenChange={setShowDefaultPermissionsModal}
        onSubmit={handleUpdateDefaultPermissions}
        initialPermissions={defaultPermissions}
      />
    </div>
  );
}