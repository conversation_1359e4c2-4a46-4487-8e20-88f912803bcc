'use client';

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { ConfirmModal } from '@/components/ui/modal';
import {
  Settings,
  Users,
  Shield,
  Trash2,
  UserPlus,
  Wrench
} from 'lucide-react';
import { useI18n } from '@/lib/i18n';
import { toast } from 'sonner';
import type { Group } from '@/lib/api/groups';
import { GroupDisplay } from './GroupDisplay';
import { GroupPermissions } from './GroupPermissions';
import { GroupUsers } from './GroupUsers';

interface EditGroupModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  group: Group | null;
  onSubmit: (group: any) => Promise<void>;
  onDelete: (groupId: string) => Promise<void>;
  users?: any[];
}

export function EditGroupModal({ 
  open, 
  onOpenChange, 
  group, 
  onSubmit, 
  onDelete,
  users = []
}: EditGroupModalProps) {
  const { t } = useI18n();
  const [loading, setLoading] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [activeTab, setActiveTab] = useState('display');
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    permissions: {},
    user_ids: [] as string[]
  });

  // Initialize form data when group changes
  useEffect(() => {
    if (group && open) {
      setFormData({
        name: group.name || '',
        description: group.description || '',
        permissions: group.permissions || {},
        user_ids: group.user_ids || []
      });
      setActiveTab('display');
    }
  }, [group, open]);

  const handleSubmit = async () => {
    if (!group) return;

    setLoading(true);
    try {
      await onSubmit({
        id: group.id,
        name: formData.name,
        description: formData.description,
        permissions: formData.permissions,
        user_ids: formData.user_ids
      });
      
      toast.success(t('Group updated successfully'));
      onOpenChange(false);
    } catch (error) {
      console.error('Error updating group:', error);
      toast.error(t('Failed to update group'));
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!group) return;

    setLoading(true);
    try {
      await onDelete(group.id);
      toast.success(t('Group deleted successfully'));
      onOpenChange(false);
    } catch (error) {
      console.error('Error deleting group:', error);
      toast.error(t('Failed to delete group'));
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (!group) return null;

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] !bg-white dark:!bg-gray-900">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              {t('Edit User Group')}
            </DialogTitle>
            <DialogDescription>
              {t('Manage group settings, permissions, and user assignments.')}
            </DialogDescription>
          </DialogHeader>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="display" className="flex items-center gap-2">
                <Settings className="w-4 h-4" />
                {t('Display')}
              </TabsTrigger>
              <TabsTrigger value="permissions" className="flex items-center gap-2">
                <Wrench className="w-4 h-4" />
                {t('Permissions')}
              </TabsTrigger>
              <TabsTrigger value="users" className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                {t('Users')}
                <Badge variant="secondary" className="ml-1">
                  {formData.user_ids.length}
                </Badge>
              </TabsTrigger>
            </TabsList>

            <ScrollArea className="h-[400px] mt-4">
              <TabsContent value="display" className="space-y-4">
                <GroupDisplay
                  name={formData.name}
                  description={formData.description}
                  onChange={(field, value) => updateFormData(field, value)}
                />
              </TabsContent>

              <TabsContent value="permissions" className="space-y-4">
                <GroupPermissions
                  permissions={formData.permissions}
                  onChange={(permissions) => updateFormData('permissions', permissions)}
                />
              </TabsContent>

              <TabsContent value="users" className="space-y-4">
                <GroupUsers
                  selectedUserIds={formData.user_ids}
                  availableUsers={users}
                  onChange={(userIds) => updateFormData('user_ids', userIds)}
                />
              </TabsContent>
            </ScrollArea>

            <div className="flex justify-between pt-4 border-t">
              <Button
                variant="destructive"
                onClick={() => setShowDeleteConfirm(true)}
                disabled={loading}
                className="flex items-center gap-2"
              >
                <Trash2 className="w-4 h-4" />
                {t('Delete Group')}
              </Button>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={loading}
                >
                  {t('Cancel')}
                </Button>
                <Button
                  onClick={handleSubmit}
                  disabled={loading}
                >
                  {loading ? t('Saving...') : t('Save Changes')}
                </Button>
              </div>
            </div>
          </Tabs>
        </DialogContent>
      </Dialog>

      <ConfirmModal
        show={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={handleDelete}
        title={t('Delete Group')}
        message={t('Are you sure you want to delete this group? This action cannot be undone.')}
        confirmText={t('Delete')}
        variant="destructive"
      />
    </>
  );
}
