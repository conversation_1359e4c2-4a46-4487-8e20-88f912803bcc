'use client';

import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useI18n } from '@/lib/i18n';

interface GroupDisplayProps {
  name: string;
  description: string;
  onChange: (field: string, value: string) => void;
}

export function GroupDisplay({ name, description, onChange }: GroupDisplayProps) {
  const { t } = useI18n();

  return (
    <div className="space-y-4 p-4">
      <div className="space-y-2">
        <Label htmlFor="group-name">{t('Group Name')} *</Label>
        <Input
          id="group-name"
          value={name}
          onChange={(e) => onChange('name', e.target.value)}
          placeholder={t('Enter group name')}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="group-description">{t('Description')}</Label>
        <Textarea
          id="group-description"
          value={description}
          onChange={(e) => onChange('description', e.target.value)}
          placeholder={t('Enter group description (optional)')}
          rows={4}
        />
      </div>
    </div>
  );
}
