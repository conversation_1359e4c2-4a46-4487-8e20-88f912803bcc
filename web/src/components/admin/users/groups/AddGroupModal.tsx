'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON>alogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useI18n } from '@/lib/i18n';
import { toast } from 'sonner';

interface AddGroupModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (groupData: { name: string; description: string }) => Promise<void>;
}

export function AddGroupModal({ open, onOpenChange, onSubmit }: AddGroupModalProps) {
  const { t } = useI18n();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      newErrors.name = t('Group name is required');
    } else if (formData.name.length < 2) {
      newErrors.name = t('Group name must be at least 2 characters');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error(t('Please fix the validation errors'));
      return;
    }

    setLoading(true);
    
    try {
      await onSubmit({
        name: formData.name.trim(),
        description: formData.description.trim()
      });
      
      toast.success(t('Group created successfully'));
      handleClose();
    } catch (error) {
      console.error('Error creating group:', error);
      toast.error(t('Failed to create group'));
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({
      name: '',
      description: ''
    });
    setErrors({});
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px] !bg-white dark:!bg-gray-900">
        <DialogHeader>
          <DialogTitle>{t('Add User Group')}</DialogTitle>
          <DialogDescription>
            {t('Create a new user group to organize users and assign permissions.')}
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">{t('Group Name')} *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder={t('Enter group name')}
              className={errors.name ? 'border-red-500' : ''}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">{t('Description')}</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder={t('Enter group description (optional)')}
              rows={3}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={loading}
            >
              {t('Cancel')}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? t('Creating...') : t('Create Group')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
