'use client';

import React from 'react';
import { Switch } from '@/components/ui/switch';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  Briefcase, 
  Share, 
  MessageSquare, 
  Zap,
  Database,
  FileText,
  Key,
  Settings,
  Upload,
  Download,
  Mic,
  Image,
  Code,
  Search,
  StickyNote,
  Globe
} from 'lucide-react';
import { useI18n } from '@/lib/i18n';

interface Permission {
  workspace: {
    models: boolean;
    knowledge: boolean;
    prompts: boolean;
    tools: boolean;
  };
  sharing: {
    public_models: boolean;
    public_knowledge: boolean;
    public_prompts: boolean;
    public_tools: boolean;
  };
  chat: {
    controls: boolean;
    system_prompt: boolean;
    file_upload: boolean;
    delete: boolean;
    edit: boolean;
    share: boolean;
    export: boolean;
    stt: boolean;
    tts: boolean;
    call: boolean;
    multiple_models: boolean;
    temporary: boolean;
    temporary_enforced: boolean;
  };
  features: {
    direct_tool_servers: boolean;
    web_search: boolean;
    image_generation: boolean;
    code_interpreter: boolean;
    notes: boolean;
  };
}

interface GroupPermissionsProps {
  permissions: any;
  onChange: (permissions: Permission) => void;
}

interface PermissionItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  checked: boolean;
  onChange: (checked: boolean) => void;
}

function PermissionItem({ icon, title, description, checked, onChange }: PermissionItemProps) {
  return (
    <div className="flex items-center justify-between py-2">
      <div className="flex items-center gap-3 flex-1">
        <div className="text-gray-500 dark:text-gray-400">
          {icon}
        </div>
        <div className="flex-1">
          <div className="text-sm font-medium">{title}</div>
          <div className="text-xs text-gray-500 dark:text-gray-400">{description}</div>
        </div>
      </div>
      <Switch
        checked={checked}
        onCheckedChange={onChange}
      />
    </div>
  );
}

export function GroupPermissions({ permissions, onChange }: GroupPermissionsProps) {
  const { t } = useI18n();

  // Ensure permissions object has all required fields with defaults
  const defaultPermissions: Permission = {
    workspace: {
      models: false,
      knowledge: false,
      prompts: false,
      tools: false
    },
    sharing: {
      public_models: false,
      public_knowledge: false,
      public_prompts: false,
      public_tools: false
    },
    chat: {
      controls: true,
      system_prompt: true,
      file_upload: true,
      delete: true,
      edit: true,
      share: true,
      export: true,
      stt: true,
      tts: true,
      call: true,
      multiple_models: true,
      temporary: true,
      temporary_enforced: false
    },
    features: {
      direct_tool_servers: false,
      web_search: true,
      image_generation: true,
      code_interpreter: true,
      notes: true
    }
  };

  const currentPermissions = {
    ...defaultPermissions,
    ...permissions,
    workspace: { ...defaultPermissions.workspace, ...permissions.workspace },
    sharing: { ...defaultPermissions.sharing, ...permissions.sharing },
    chat: { ...defaultPermissions.chat, ...permissions.chat },
    features: { ...defaultPermissions.features, ...permissions.features }
  };

  const updatePermission = (category: keyof Permission, key: string, value: boolean) => {
    const updated = {
      ...currentPermissions,
      [category]: {
        ...currentPermissions[category],
        [key]: value
      }
    };
    onChange(updated);
  };

  return (
    <div className="space-y-4 p-4">
      {/* Workspace Permissions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <Briefcase className="w-4 h-4" />
            {t('Workspace Permissions')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <PermissionItem
            icon={<Database className="w-4 h-4" />}
            title={t('Models Access')}
            description={t('Access to view and use AI models')}
            checked={currentPermissions.workspace.models}
            onChange={(checked) => updatePermission('workspace', 'models', checked)}
          />
          <Separator />
          <PermissionItem
            icon={<FileText className="w-4 h-4" />}
            title={t('Knowledge Access')}
            description={t('Access to knowledge base and documents')}
            checked={currentPermissions.workspace.knowledge}
            onChange={(checked) => updatePermission('workspace', 'knowledge', checked)}
          />
          <Separator />
          <PermissionItem
            icon={<Key className="w-4 h-4" />}
            title={t('Prompts Access')}
            description={t('Access to create and manage prompts')}
            checked={currentPermissions.workspace.prompts}
            onChange={(checked) => updatePermission('workspace', 'prompts', checked)}
          />
          <Separator />
          <PermissionItem
            icon={<Settings className="w-4 h-4" />}
            title={t('Tools Access')}
            description={t('Access to tools and integrations')}
            checked={currentPermissions.workspace.tools}
            onChange={(checked) => updatePermission('workspace', 'tools', checked)}
          />
        </CardContent>
      </Card>

      {/* Sharing Permissions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <Share className="w-4 h-4" />
            {t('Sharing Permissions')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <PermissionItem
            icon={<Database className="w-4 h-4" />}
            title={t('Models Public Sharing')}
            description={t('Allow sharing models publicly')}
            checked={currentPermissions.sharing.public_models}
            onChange={(checked) => updatePermission('sharing', 'public_models', checked)}
          />
          <Separator />
          <PermissionItem
            icon={<FileText className="w-4 h-4" />}
            title={t('Knowledge Public Sharing')}
            description={t('Allow sharing knowledge base publicly')}
            checked={currentPermissions.sharing.public_knowledge}
            onChange={(checked) => updatePermission('sharing', 'public_knowledge', checked)}
          />
          <Separator />
          <PermissionItem
            icon={<Key className="w-4 h-4" />}
            title={t('Prompts Public Sharing')}
            description={t('Allow sharing prompts publicly')}
            checked={currentPermissions.sharing.public_prompts}
            onChange={(checked) => updatePermission('sharing', 'public_prompts', checked)}
          />
          <Separator />
          <PermissionItem
            icon={<Settings className="w-4 h-4" />}
            title={t('Tools Public Sharing')}
            description={t('Allow sharing tools publicly')}
            checked={currentPermissions.sharing.public_tools}
            onChange={(checked) => updatePermission('sharing', 'public_tools', checked)}
          />
        </CardContent>
      </Card>

      {/* Chat Permissions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <MessageSquare className="w-4 h-4" />
            {t('Chat Permissions')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <PermissionItem
            icon={<Upload className="w-4 h-4" />}
            title={t('Allow File Upload')}
            description={t('Allow uploading files in chat')}
            checked={currentPermissions.chat.file_upload}
            onChange={(checked) => updatePermission('chat', 'file_upload', checked)}
          />
          <Separator />
          <PermissionItem
            icon={<Settings className="w-4 h-4" />}
            title={t('Allow Chat Controls')}
            description={t('Allow access to chat controls')}
            checked={currentPermissions.chat.controls}
            onChange={(checked) => updatePermission('chat', 'controls', checked)}
          />
          <Separator />
          <PermissionItem
            icon={<Key className="w-4 h-4" />}
            title={t('Allow Chat System Prompt')}
            description={t('Allow setting system prompts')}
            checked={currentPermissions.chat.system_prompt}
            onChange={(checked) => updatePermission('chat', 'system_prompt', checked)}
          />
          <Separator />
          <PermissionItem
            icon={<Download className="w-4 h-4" />}
            title={t('Allow Chat Export')}
            description={t('Allow exporting chat conversations')}
            checked={currentPermissions.chat.export}
            onChange={(checked) => updatePermission('chat', 'export', checked)}
          />
          <Separator />
          <PermissionItem
            icon={<Mic className="w-4 h-4" />}
            title={t('Allow Voice Features')}
            description={t('Allow speech-to-text and text-to-speech')}
            checked={currentPermissions.chat.stt}
            onChange={(checked) => updatePermission('chat', 'stt', checked)}
          />
        </CardContent>
      </Card>

      {/* Feature Permissions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-base">
            <Zap className="w-4 h-4" />
            {t('Feature Permissions')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <PermissionItem
            icon={<Search className="w-4 h-4" />}
            title={t('Web Search')}
            description={t('Allow web search functionality')}
            checked={currentPermissions.features.web_search}
            onChange={(checked) => updatePermission('features', 'web_search', checked)}
          />
          <Separator />
          <PermissionItem
            icon={<Image className="w-4 h-4" />}
            title={t('Image Generation')}
            description={t('Allow image generation features')}
            checked={currentPermissions.features.image_generation}
            onChange={(checked) => updatePermission('features', 'image_generation', checked)}
          />
          <Separator />
          <PermissionItem
            icon={<Code className="w-4 h-4" />}
            title={t('Code Interpreter')}
            description={t('Allow code execution and interpretation')}
            checked={currentPermissions.features.code_interpreter}
            onChange={(checked) => updatePermission('features', 'code_interpreter', checked)}
          />
          <Separator />
          <PermissionItem
            icon={<StickyNote className="w-4 h-4" />}
            title={t('Notes')}
            description={t('Allow notes and documentation features')}
            checked={currentPermissions.features.notes}
            onChange={(checked) => updatePermission('features', 'notes', checked)}
          />
          <Separator />
          <PermissionItem
            icon={<Globe className="w-4 h-4" />}
            title={t('Direct Tool Servers')}
            description={t('Allow direct access to tool servers')}
            checked={currentPermissions.features.direct_tool_servers}
            onChange={(checked) => updatePermission('features', 'direct_tool_servers', checked)}
          />
        </CardContent>
      </Card>
    </div>
  );
}
