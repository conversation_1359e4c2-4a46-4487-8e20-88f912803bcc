'use client';

import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Shield } from 'lucide-react';
import { useI18n } from '@/lib/i18n';
import { toast } from 'sonner';
import { GroupPermissions } from './GroupPermissions';

interface Permission {
  workspace: {
    models: boolean;
    knowledge: boolean;
    prompts: boolean;
    tools: boolean;
  };
  sharing: {
    public_models: boolean;
    public_knowledge: boolean;
    public_prompts: boolean;
    public_tools: boolean;
  };
  chat: {
    controls: boolean;
    system_prompt: boolean;
    file_upload: boolean;
    delete: boolean;
    edit: boolean;
    share: boolean;
    export: boolean;
    stt: boolean;
    tts: boolean;
    call: boolean;
    multiple_models: boolean;
    temporary: boolean;
    temporary_enforced: boolean;
  };
  features: {
    direct_tool_servers: boolean;
    web_search: boolean;
    image_generation: boolean;
    code_interpreter: boolean;
    notes: boolean;
  };
}

interface DefaultPermissionsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (permissions: Permission) => Promise<void>;
  initialPermissions?: Permission;
}

export function DefaultPermissionsModal({ 
  open, 
  onOpenChange, 
  onSubmit,
  initialPermissions
}: DefaultPermissionsModalProps) {
  const { t } = useI18n();
  const [loading, setLoading] = useState(false);
  
  const defaultPermissions: Permission = {
    workspace: {
      models: false,
      knowledge: false,
      prompts: false,
      tools: false
    },
    sharing: {
      public_models: false,
      public_knowledge: false,
      public_prompts: false,
      public_tools: false
    },
    chat: {
      controls: true,
      system_prompt: true,
      file_upload: true,
      delete: true,
      edit: true,
      share: true,
      export: true,
      stt: true,
      tts: true,
      call: true,
      multiple_models: true,
      temporary: true,
      temporary_enforced: false
    },
    features: {
      direct_tool_servers: false,
      web_search: true,
      image_generation: true,
      code_interpreter: true,
      notes: true
    }
  };

  const [permissions, setPermissions] = useState<Permission>(defaultPermissions);

  useEffect(() => {
    if (open) {
      setPermissions(initialPermissions || defaultPermissions);
    }
  }, [open, initialPermissions]);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      await onSubmit(permissions);
      toast.success(t('Default permissions updated successfully'));
      onOpenChange(false);
    } catch (error) {
      console.error('Error updating default permissions:', error);
      toast.error(t('Failed to update default permissions'));
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    setPermissions(defaultPermissions);
    toast.info(t('Permissions reset to defaults'));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] !bg-white dark:!bg-gray-900">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            {t('Edit Default Permissions')}
          </DialogTitle>
          <DialogDescription>
            {t('Set default permissions that will be applied to all new users. These can be overridden by group permissions.')}
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="h-[400px] mt-4">
          <GroupPermissions
            permissions={permissions}
            onChange={setPermissions}
          />
        </ScrollArea>

        <DialogFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={loading}
          >
            {t('Reset to Defaults')}
          </Button>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={loading}
            >
              {t('Cancel')}
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={loading}
            >
              {loading ? t('Saving...') : t('Save Changes')}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
