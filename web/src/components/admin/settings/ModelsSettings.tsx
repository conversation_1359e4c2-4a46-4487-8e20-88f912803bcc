'use client';

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Modal, ModalBody, ModalHeader } from '@/components/ui/modal';
import { Spinner } from '@/components/ui/spinner';
import { toast } from 'sonner';
import { useAuthStore } from '@/lib/stores';
import { getModels, getBaseModels, type Model } from '@/lib/api/models';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  Settings,
  Download,
  Upload,
  Bot,
  Zap,
  Globe
} from 'lucide-react';

// Remove local Model interface since we're importing it from the API

interface ModelsSettingsProps {
  onSettingsChange?: () => void;
}

export const ModelsSettings: React.FC<ModelsSettingsProps> = ({
  onSettingsChange
}) => {
  const { token } = useAuthStore();
  const [models, setModels] = useState<Model[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingModel, setEditingModel] = useState<Model | null>(null);
  const [testingModel, setTestingModel] = useState<string | null>(null);

  // Load models on component mount
  useEffect(() => {
    if (token) {
      loadModels();
    }
  }, [token]);

  const loadModels = async () => {
    if (!token) return;
    
    setIsLoading(true);
    try {
      // Load both regular models and base models
      const [modelsResponse, baseModelsResponse] = await Promise.all([
        getModels(token).catch(() => ({ data: [] })),
        getBaseModels(token).catch(() => ({ data: [] }))
      ]);
      
      // Combine and format models data
      const allModels = [
        ...(modelsResponse.data || []),
        ...(baseModelsResponse.data || [])
      ];
      
      // Transform to our Model interface format
      const formattedModels: Model[] = allModels.map((model: any) => ({
        id: model.id || model.name,
        name: model.name || model.id,
        provider: model.owned_by || model.provider || 'Unknown',
        base_url: model.base_url,
        api_key: model.api_key,
        enabled: model.enabled ?? true,
        visible: model.visible ?? true,
        temperature: model.temperature || 0.7,
        max_tokens: model.max_tokens || 4096,
        description: model.description || model.info?.meta?.description || '',
        capabilities: model.capabilities || ['text'],
        pricing: model.pricing || { input: 0, output: 0 }
      }));
      
      setModels(formattedModels);
    } catch (error) {
      toast.error('Failed to load models');
      console.error('Load models error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleModel = async (modelId: string, field: 'enabled' | 'visible') => {
    try {
      setModels(prev => prev.map(model => 
        model.id === modelId 
          ? { ...model, [field]: !model[field] }
          : model
      ));
      onSettingsChange?.();
      toast.success(`Model ${field} status updated`);
    } catch (error) {
      toast.error(`Failed to update model ${field} status`);
    }
  };

  const handleDeleteModel = async (modelId: string) => {
    const confirmed = window.confirm('Are you sure you want to delete this model?');
    if (!confirmed) return;

    try {
      setModels(prev => prev.filter(model => model.id !== modelId));
      onSettingsChange?.();
      toast.success('Model deleted successfully');
    } catch (error) {
      toast.error('Failed to delete model');
    }
  };

  const handleTestModel = async (modelId: string) => {
    setTestingModel(modelId);
    try {
      // Simulate API test
      await new Promise(resolve => setTimeout(resolve, 2000));
      toast.success('Model test successful');
    } catch (error) {
      toast.error('Model test failed');
    } finally {
      setTestingModel(null);
    }
  };

  const handleExportModels = () => {
    const dataStr = JSON.stringify(models, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'models-config.json';
    link.click();
    URL.revokeObjectURL(url);
  };

  const handleImportModels = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedModels = JSON.parse(e.target?.result as string);
        setModels(importedModels);
        onSettingsChange?.();
        toast.success('Models imported successfully');
      } catch (error) {
        toast.error('Failed to import models');
      }
    };
    reader.readAsText(file);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Models Configuration
          </h2>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            Manage AI models and their settings
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportModels}
          >
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          
          <label>
            <Button
              variant="outline"
              size="sm"
              as="span"
            >
              <Upload className="w-4 h-4 mr-2" />
              Import
            </Button>
            <input
              type="file"
              accept=".json"
              onChange={handleImportModels}
              className="hidden"
            />
          </label>
          
          <Button onClick={() => setShowAddModal(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Add Model
          </Button>
        </div>
      </div>

      {/* Models List */}
      <div className="space-y-4">
        {models.map((model) => (
          <div
            key={model.id}
            className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <Bot className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  <h3 className="font-medium text-gray-900 dark:text-gray-100">
                    {model.name}
                  </h3>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {model.provider}
                  </span>
                </div>
                
                {model.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    {model.description}
                  </p>
                )}
                
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <span className="text-gray-500">Temperature:</span>
                    <span>{model.temperature}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-gray-500">Max Tokens:</span>
                    <span>{model.max_tokens}</span>
                  </div>
                  {model.pricing && (
                    <div className="flex items-center gap-2">
                      <span className="text-gray-500">Pricing:</span>
                      <span>${model.pricing.input}/${model.pricing.output} per 1K tokens</span>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {/* Enabled Toggle */}
                <div className="flex items-center gap-2">
                  <Label htmlFor={`enabled-${model.id}`} className="text-sm">
                    Enabled
                  </Label>
                  <Switch
                    id={`enabled-${model.id}`}
                    checked={model.enabled}
                    onCheckedChange={() => handleToggleModel(model.id, 'enabled')}
                  />
                </div>
                
                {/* Visible Toggle */}
                <div className="flex items-center gap-2">
                  <Label htmlFor={`visible-${model.id}`} className="text-sm">
                    Visible
                  </Label>
                  <Switch
                    id={`visible-${model.id}`}
                    checked={model.visible}
                    onCheckedChange={() => handleToggleModel(model.id, 'visible')}
                  />
                </div>
                
                {/* Actions */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleTestModel(model.id)}
                  disabled={testingModel === model.id}
                >
                  {testingModel === model.id ? (
                    <Spinner size="sm" />
                  ) : (
                    <Zap className="w-4 h-4" />
                  )}
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setEditingModel(model)}
                >
                  <Edit className="w-4 h-4" />
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleDeleteModel(model.id)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {models.length === 0 && (
        <div className="text-center py-8">
          <Bot className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            No models configured
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            Add your first AI model to get started
          </p>
          <Button onClick={() => setShowAddModal(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Add Model
          </Button>
        </div>
      )}

      {/* Add/Edit Model Modal */}
      <ModelEditModal
        show={showAddModal || !!editingModel}
        onClose={() => {
          setShowAddModal(false);
          setEditingModel(null);
        }}
        model={editingModel}
        onSave={(model) => {
          if (editingModel) {
            setModels(prev => prev.map(m => m.id === model.id ? model : m));
          } else {
            setModels(prev => [...prev, { ...model, id: Date.now().toString() }]);
          }
          onSettingsChange?.();
          setShowAddModal(false);
          setEditingModel(null);
        }}
      />
    </div>
  );
};

// Model Edit Modal Component
interface ModelEditModalProps {
  show: boolean;
  onClose: () => void;
  model?: Model | null;
  onSave: (model: Model) => void;
}

const ModelEditModal: React.FC<ModelEditModalProps> = ({
  show,
  onClose,
  model,
  onSave
}) => {
  const [formData, setFormData] = useState<Partial<Model>>({
    name: '',
    provider: '',
    base_url: '',
    api_key: '',
    enabled: true,
    visible: true,
    temperature: 0.7,
    max_tokens: 4096,
    description: ''
  });

  useEffect(() => {
    if (model) {
      setFormData(model);
    } else {
      setFormData({
        name: '',
        provider: '',
        base_url: '',
        api_key: '',
        enabled: true,
        visible: true,
        temperature: 0.7,
        max_tokens: 4096,
        description: ''
      });
    }
  }, [model]);

  const handleSave = () => {
    if (!formData.name || !formData.provider) {
      toast.error('Please fill in required fields');
      return;
    }

    onSave(formData as Model);
    toast.success(model ? 'Model updated' : 'Model added');
  };

  return (
    <Modal
      show={show}
      onClose={onClose}
      title={model ? 'Edit Model' : 'Add Model'}
      size="lg"
    >
      <ModalBody>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                value={formData.name || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., GPT-4"
              />
            </div>
            <div>
              <Label htmlFor="provider">Provider *</Label>
              <Input
                id="provider"
                value={formData.provider || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, provider: e.target.value }))}
                placeholder="e.g., OpenAI"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="base_url">Base URL</Label>
            <Input
              id="base_url"
              value={formData.base_url || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, base_url: e.target.value }))}
              placeholder="https://api.openai.com/v1"
            />
          </div>

          <div>
            <Label htmlFor="api_key">API Key</Label>
            <Input
              id="api_key"
              type="password"
              value={formData.api_key || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, api_key: e.target.value }))}
              placeholder="Enter API key"
            />
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Model description..."
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="temperature">Temperature</Label>
              <Input
                id="temperature"
                type="number"
                min="0"
                max="2"
                step="0.1"
                value={formData.temperature || 0.7}
                onChange={(e) => setFormData(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
              />
            </div>
            <div>
              <Label htmlFor="max_tokens">Max Tokens</Label>
              <Input
                id="max_tokens"
                type="number"
                min="1"
                value={formData.max_tokens || 4096}
                onChange={(e) => setFormData(prev => ({ ...prev, max_tokens: parseInt(e.target.value) }))}
              />
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Switch
                id="enabled"
                checked={formData.enabled || false}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, enabled: checked }))}
              />
              <Label htmlFor="enabled">Enabled</Label>
            </div>
            <div className="flex items-center gap-2">
              <Switch
                id="visible"
                checked={formData.visible || false}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, visible: checked }))}
              />
              <Label htmlFor="visible">Visible to users</Label>
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              {model ? 'Update' : 'Add'} Model
            </Button>
          </div>
        </div>
      </ModalBody>
    </Modal>
  );
};
