'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Database, 
  Download, 
  Upload, 
  RefreshCw, 
  Trash2, 
  FileText, 
  Settings,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  HardDrive,
  Archive,
  Share
} from 'lucide-react';
import { useI18n } from '@/lib/i18n';
import { useAuthStore } from '@/lib/stores';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface DatabaseStats {
  total_users: number;
  total_chats: number;
  total_messages: number;
  total_functions: number;
  database_size: string;
  last_backup: string | null;
}

export const DatabaseManagement: React.FC = () => {
  const { t } = useI18n();
  const { token } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [importProgress, setImportProgress] = useState(0);
  const [stats, setStats] = useState<DatabaseStats>({
    total_users: 156,
    total_chats: 2847,
    total_messages: 18934,
    total_functions: 23,
    database_size: '245.7 MB',
    last_backup: '2024-01-15T10:30:00Z'
  });

  const handleExportConfig = async () => {
    if (!token) return;

    setLoading(true);
    setExportProgress(0);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      // TODO: Implement actual API call
      const response = await fetch('/api/admin/database/export-config', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      clearInterval(progressInterval);
      setExportProgress(100);

      if (!response.ok) {
        throw new Error('Failed to export configuration');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `webui-config-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      toast.success('Configuration exported successfully');
    } catch (error) {
      console.error('Error exporting configuration:', error);
      toast.error('Failed to export configuration');
    } finally {
      setLoading(false);
      setTimeout(() => setExportProgress(0), 2000);
    }
  };

  const handleImportConfig = async (file: File) => {
    if (!token) return;

    setLoading(true);
    setImportProgress(0);

    try {
      const formData = new FormData();
      formData.append('config', file);

      // Simulate progress
      const progressInterval = setInterval(() => {
        setImportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 15;
        });
      }, 300);

      // TODO: Implement actual API call
      const response = await fetch('/api/admin/database/import-config', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      clearInterval(progressInterval);
      setImportProgress(100);

      if (!response.ok) {
        throw new Error('Failed to import configuration');
      }

      toast.success('Configuration imported successfully');
    } catch (error) {
      console.error('Error importing configuration:', error);
      toast.error('Failed to import configuration');
    } finally {
      setLoading(false);
      setTimeout(() => setImportProgress(0), 2000);
    }
  };

  const handleExportDatabase = async () => {
    if (!token) return;

    setLoading(true);
    
    try {
      // TODO: Implement actual API call
      const response = await fetch('/api/admin/database/export', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to export database');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `webui-database-${new Date().toISOString().split('T')[0]}.db`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      toast.success('Database exported successfully');
      
      // Update last backup time
      setStats(prev => ({
        ...prev,
        last_backup: new Date().toISOString()
      }));
    } catch (error) {
      console.error('Error exporting database:', error);
      toast.error('Failed to export database');
    } finally {
      setLoading(false);
    }
  };

  const handleExportChats = async () => {
    if (!token) return;

    setLoading(true);
    
    try {
      // TODO: Implement actual API call
      const response = await fetch('/api/admin/chats/export-all', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to export chats');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `all-chats-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      toast.success('All chats exported successfully');
    } catch (error) {
      console.error('Error exporting chats:', error);
      toast.error('Failed to export chats');
    } finally {
      setLoading(false);
    }
  };

  const handleResetDatabase = async () => {
    if (!token) return;

    setLoading(true);
    
    try {
      // TODO: Implement actual API call
      const response = await fetch('/api/admin/database/reset', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to reset database');
      }

      toast.success('Database reset successfully');
      
      // Reset stats
      setStats({
        total_users: 0,
        total_chats: 0,
        total_messages: 0,
        total_functions: 0,
        database_size: '0 MB',
        last_backup: null
      });
    } catch (error) {
      console.error('Error resetting database:', error);
      toast.error('Failed to reset database');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="space-y-6">
      {/* Database Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            Database Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {stats.total_users.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Users</div>
            </div>
            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {stats.total_chats.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Chats</div>
            </div>
            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {stats.total_messages.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Messages</div>
            </div>
            <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
              <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                {stats.database_size}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Size</div>
            </div>
          </div>
          
          {stats.last_backup && (
            <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                <Clock className="w-4 h-4" />
                Last backup: {formatDate(stats.last_backup)}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Configuration Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Configuration Management
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <h4 className="font-medium mb-2">Export Configuration</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Download your current system configuration as a JSON file.
              </p>
              <Button 
                onClick={handleExportConfig} 
                disabled={loading}
                className="w-full sm:w-auto"
              >
                <Download className="w-4 h-4 mr-2" />
                Export Config
              </Button>
              {exportProgress > 0 && (
                <div className="mt-2">
                  <Progress value={exportProgress} className="w-full" />
                  <p className="text-sm text-gray-500 mt-1">{exportProgress}% complete</p>
                </div>
              )}
            </div>
            
            <Separator orientation="vertical" className="hidden sm:block" />
            
            <div className="flex-1">
              <h4 className="font-medium mb-2">Import Configuration</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Upload a configuration file to restore system settings.
              </p>
              <div className="flex flex-col gap-2">
                <Input
                  type="file"
                  accept=".json"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleImportConfig(file);
                    }
                  }}
                  disabled={loading}
                />
                {importProgress > 0 && (
                  <div>
                    <Progress value={importProgress} className="w-full" />
                    <p className="text-sm text-gray-500 mt-1">{importProgress}% complete</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Export */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Archive className="w-5 h-5" />
            Data Export & Backup
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <HardDrive className="w-4 h-4" />
                <h4 className="font-medium">Full Database</h4>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Download the complete database file including all users, chats, and settings.
              </p>
              <Button 
                onClick={handleExportDatabase} 
                disabled={loading}
                variant="outline"
                className="w-full"
              >
                <Download className="w-4 h-4 mr-2" />
                Export Database
              </Button>
            </div>

            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Share className="w-4 h-4" />
                <h4 className="font-medium">All User Chats</h4>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Export all user conversations and chat history as JSON.
              </p>
              <Button 
                onClick={handleExportChats} 
                disabled={loading}
                variant="outline"
                className="w-full"
              >
                <Download className="w-4 h-4 mr-2" />
                Export Chats
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Danger Zone */}
      <Card className="border-red-200 dark:border-red-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600 dark:text-red-400">
            <AlertTriangle className="w-5 h-5" />
            Danger Zone
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <h4 className="font-medium text-red-800 dark:text-red-200 mb-2">
              Reset Database
            </h4>
            <p className="text-sm text-red-700 dark:text-red-300 mb-4">
              This will permanently delete all users, chats, messages, and settings. 
              This action cannot be undone. Make sure to create a backup first.
            </p>
            
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" disabled={loading}>
                  <Trash2 className="w-4 h-4 mr-2" />
                  Reset Database
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle className="flex items-center gap-2">
                    <AlertTriangle className="w-5 h-5 text-red-500" />
                    Reset Database
                  </AlertDialogTitle>
                  <AlertDialogDescription>
                    This will permanently delete all data in the database including:
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>All user accounts and profiles</li>
                      <li>All chat conversations and messages</li>
                      <li>All custom functions and tools</li>
                      <li>All system settings and configurations</li>
                    </ul>
                    <p className="mt-3 font-semibold text-red-600 dark:text-red-400">
                      This action cannot be undone!
                    </p>
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction 
                    onClick={handleResetDatabase}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    Yes, Reset Database
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};