'use client';

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Spinner } from '@/components/ui/spinner';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { useAuthStore } from '@/lib/stores';
import { getAppConfig, getWebhookUrl, setWebhookUrl, getVersion } from '@/lib/api/configs';
import { getAdminStats } from '@/lib/api/admin';
import {
  Settings,
  Server,
  Database,
  Shield,
  Globe,
  Clock,
  Users,
  MessageSquare,
  FileText,
  Zap
} from 'lucide-react';

interface GeneralConfig {
  // Basic Settings
  WEBUI_NAME: string;
  WEBUI_URL: string;
  WEBUI_VERSION: string;
  ENABLE_SIGNUP: boolean;
  ENABLE_LOGIN_FORM: boolean;
  DEFAULT_USER_ROLE: 'pending' | 'user' | 'admin';
  
  // Model Settings
  DEFAULT_MODELS: string;
  DEFAULT_PROMPT_SUGGESTIONS: string;
  ENABLE_MODEL_FILTER: boolean;
  MODEL_FILTER_LIST: string;
  
  // Chat Settings
  DEFAULT_SYSTEM_PROMPT: string;
  ENABLE_CHAT_DELETION: boolean;
  ENABLE_MESSAGE_RATING: boolean;
  ENABLE_COMMUNITY_SHARING: boolean;
  
  // File Upload
  ENABLE_FILE_UPLOAD: boolean;
  FILE_SIZE_LIMIT: number;
  ALLOWED_FILE_TYPES: string;
  
  // Security
  ENABLE_OAUTH_SIGNUP: boolean;
  OAUTH_MERGE_ACCOUNTS_BY_EMAIL: boolean;
  JWT_EXPIRES_IN: string;
  WEBHOOK_URL: string;
  
  // Performance
  TASK_MODEL: string;
  TITLE_GENERATION_PROMPT_TEMPLATE: string;
  ENABLE_SEARCH: boolean;
  SEARCH_QUERY_GENERATION_PROMPT_TEMPLATE: string;
}

interface GeneralSettingsProps {
  onSettingsChange?: () => void;
}

export const GeneralSettings: React.FC<GeneralSettingsProps> = ({
  onSettingsChange
}) => {
  const { token } = useAuthStore();
  const [config, setConfig] = useState<GeneralConfig>({
    WEBUI_NAME: 'Open WebUI',
    WEBUI_URL: '',
    WEBUI_VERSION: '0.3.32',
    ENABLE_SIGNUP: true,
    ENABLE_LOGIN_FORM: true,
    DEFAULT_USER_ROLE: 'pending',
    DEFAULT_MODELS: '',
    DEFAULT_PROMPT_SUGGESTIONS: '',
    ENABLE_MODEL_FILTER: false,
    MODEL_FILTER_LIST: '',
    DEFAULT_SYSTEM_PROMPT: '',
    ENABLE_CHAT_DELETION: true,
    ENABLE_MESSAGE_RATING: true,
    ENABLE_COMMUNITY_SHARING: false,
    ENABLE_FILE_UPLOAD: true,
    FILE_SIZE_LIMIT: 25,
    ALLOWED_FILE_TYPES: 'pdf,txt,docx,md',
    ENABLE_OAUTH_SIGNUP: false,
    OAUTH_MERGE_ACCOUNTS_BY_EMAIL: false,
    JWT_EXPIRES_IN: '7d',
    WEBHOOK_URL: '',
    TASK_MODEL: '',
    TITLE_GENERATION_PROMPT_TEMPLATE: '',
    ENABLE_SEARCH: true,
    SEARCH_QUERY_GENERATION_PROMPT_TEMPLATE: ''
  });

  const [isLoading, setIsLoading] = useState(true);
  const [systemInfo, setSystemInfo] = useState({
    uptime: '0 days, 0 hours',
    totalUsers: 0,
    totalChats: 0,
    totalMessages: 0,
    storageUsed: '0 GB',
    memoryUsage: '0 GB'
  });

  useEffect(() => {
    if (token) {
      loadConfig();
      loadSystemInfo();
    }
  }, [token]);

  const loadConfig = async () => {
    if (!token) return;
    
    setIsLoading(true);
    try {
      // Load app config from backend
      const [appConfig, versionInfo, webhookInfo] = await Promise.all([
        getAppConfig(),
        getVersion(),
        getWebhookUrl(token)
      ]);
      
      setConfig(prevConfig => ({
        ...prevConfig,
        WEBUI_NAME: appConfig.name || prevConfig.WEBUI_NAME,
        WEBUI_VERSION: versionInfo.version || prevConfig.WEBUI_VERSION,
        ENABLE_SIGNUP: appConfig.features?.enable_signup ?? prevConfig.ENABLE_SIGNUP,
        ENABLE_LOGIN_FORM: appConfig.features?.enable_login_form ?? prevConfig.ENABLE_LOGIN_FORM,
        ENABLE_MESSAGE_RATING: appConfig.features?.enable_message_rating ?? prevConfig.ENABLE_MESSAGE_RATING,
        ENABLE_COMMUNITY_SHARING: appConfig.features?.enable_community_sharing ?? prevConfig.ENABLE_COMMUNITY_SHARING,
        WEBHOOK_URL: webhookInfo.url || prevConfig.WEBHOOK_URL,
        DEFAULT_MODELS: appConfig.default_models ? appConfig.default_models.join(',') : prevConfig.DEFAULT_MODELS,
        DEFAULT_PROMPT_SUGGESTIONS: appConfig.default_prompt_suggestions ? appConfig.default_prompt_suggestions.join(',') : prevConfig.DEFAULT_PROMPT_SUGGESTIONS
      }));
    } catch (error) {
      toast.error('Failed to load general configuration');
      console.error('Load config error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadSystemInfo = async () => {
    if (!token) return;
    
    try {
      // Load system statistics from admin API
      const stats = await getAdminStats(token);
      
      setSystemInfo({
        uptime: '0 days, 0 hours', // Backend doesn't provide uptime, would need separate endpoint
        totalUsers: stats.totalUsers || 0,
        totalChats: stats.totalChats || 0,
        totalMessages: 0, // This might need a separate endpoint
        storageUsed: '0 GB', // This might need a separate endpoint  
        memoryUsage: `${stats.resourceUsage?.memory || 0} GB`
      });
    } catch (error) {
      console.error('Failed to load system info:', error);
    }
  };

  const handleConfigChange = (key: keyof GeneralConfig, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }));
    onSettingsChange?.();
  };

  const saveConfig = async () => {
    if (!token) return;
    
    try {
      // Save webhook URL if it changed
      await setWebhookUrl(token, config.WEBHOOK_URL);
      
      toast.success('Settings saved successfully');
    } catch (error) {
      toast.error('Failed to save settings');
      console.error('Save config error:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
          General Configuration
        </h2>
        <p className="text-gray-500 dark:text-gray-400 mt-1">
          Configure basic application settings and behavior
        </p>
      </div>

      {/* System Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2 mb-2">
            <Clock className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Uptime</span>
          </div>
          <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
            {systemInfo.uptime}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2 mb-2">
            <Users className="w-4 h-4 text-green-600 dark:text-green-400" />
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Users</span>
          </div>
          <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
            {systemInfo.totalUsers.toLocaleString()}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2 mb-2">
            <MessageSquare className="w-4 h-4 text-purple-600 dark:text-purple-400" />
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Chats</span>
          </div>
          <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
            {systemInfo.totalChats.toLocaleString()}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2 mb-2">
            <FileText className="w-4 h-4 text-orange-600 dark:text-orange-400" />
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Messages</span>
          </div>
          <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
            {systemInfo.totalMessages.toLocaleString()}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2 mb-2">
            <Database className="w-4 h-4 text-cyan-600 dark:text-cyan-400" />
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Storage</span>
          </div>
          <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
            {systemInfo.storageUsed}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2 mb-2">
            <Server className="w-4 h-4 text-red-600 dark:text-red-400" />
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Memory</span>
          </div>
          <div className="text-lg font-bold text-gray-900 dark:text-gray-100">
            {systemInfo.memoryUsage}
          </div>
        </div>
      </div>

      <Separator />

      {/* Basic Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center gap-2">
          <Settings className="w-5 h-5" />
          Basic Settings
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="webui-name">Application Name</Label>
            <Input
              id="webui-name"
              value={config.WEBUI_NAME}
              onChange={(e) => handleConfigChange('WEBUI_NAME', e.target.value)}
              placeholder="Open WebUI"
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="webui-url">Application URL</Label>
            <Input
              id="webui-url"
              value={config.WEBUI_URL}
              onChange={(e) => handleConfigChange('WEBUI_URL', e.target.value)}
              placeholder="https://your-domain.com"
              className="mt-1"
            />
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Label>Version:</Label>
          <Badge variant="outline">{config.WEBUI_VERSION}</Badge>
        </div>
      </div>

      <Separator />

      {/* User Management */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center gap-2">
          <Users className="w-5 h-5" />
          User Management
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div>
              <Label>Enable Signup</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">Allow new user registration</p>
            </div>
            <Switch
              checked={config.ENABLE_SIGNUP}
              onCheckedChange={(checked) => handleConfigChange('ENABLE_SIGNUP', checked)}
            />
          </div>

          <div className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div>
              <Label>Show Login Form</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">Display login form on homepage</p>
            </div>
            <Switch
              checked={config.ENABLE_LOGIN_FORM}
              onCheckedChange={(checked) => handleConfigChange('ENABLE_LOGIN_FORM', checked)}
            />
          </div>

          <div className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div>
              <Label>OAuth Signup</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">Enable OAuth registration</p>
            </div>
            <Switch
              checked={config.ENABLE_OAUTH_SIGNUP}
              onCheckedChange={(checked) => handleConfigChange('ENABLE_OAUTH_SIGNUP', checked)}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="default-role">Default User Role</Label>
            <select
              id="default-role"
              value={config.DEFAULT_USER_ROLE}
              onChange={(e) => handleConfigChange('DEFAULT_USER_ROLE', e.target.value as any)}
              className="w-full mt-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800"
            >
              <option value="pending">Pending</option>
              <option value="user">User</option>
              <option value="admin">Admin</option>
            </select>
          </div>

          <div>
            <Label htmlFor="jwt-expires">JWT Token Expiry</Label>
            <Input
              id="jwt-expires"
              value={config.JWT_EXPIRES_IN}
              onChange={(e) => handleConfigChange('JWT_EXPIRES_IN', e.target.value)}
              placeholder="7d"
              className="mt-1"
            />
          </div>
        </div>
      </div>

      <Separator />

      {/* Chat Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center gap-2">
          <MessageSquare className="w-5 h-5" />
          Chat Settings
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div>
              <Label>Chat Deletion</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">Allow users to delete chats</p>
            </div>
            <Switch
              checked={config.ENABLE_CHAT_DELETION}
              onCheckedChange={(checked) => handleConfigChange('ENABLE_CHAT_DELETION', checked)}
            />
          </div>

          <div className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div>
              <Label>Message Rating</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">Enable message rating system</p>
            </div>
            <Switch
              checked={config.ENABLE_MESSAGE_RATING}
              onCheckedChange={(checked) => handleConfigChange('ENABLE_MESSAGE_RATING', checked)}
            />
          </div>

          <div className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div>
              <Label>Community Sharing</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">Enable chat sharing features</p>
            </div>
            <Switch
              checked={config.ENABLE_COMMUNITY_SHARING}
              onCheckedChange={(checked) => handleConfigChange('ENABLE_COMMUNITY_SHARING', checked)}
            />
          </div>
        </div>

        <div>
          <Label htmlFor="default-system-prompt">Default System Prompt</Label>
          <Textarea
            id="default-system-prompt"
            value={config.DEFAULT_SYSTEM_PROMPT}
            onChange={(e) => handleConfigChange('DEFAULT_SYSTEM_PROMPT', e.target.value)}
            placeholder="You are a helpful AI assistant..."
            className="mt-1"
            rows={4}
          />
        </div>
      </div>

      <Separator />

      {/* File Upload Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center gap-2">
          <FileText className="w-5 h-5" />
          File Upload
        </h3>
        
        <div className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div>
            <Label>Enable File Upload</Label>
            <p className="text-sm text-gray-500 dark:text-gray-400">Allow users to upload files</p>
          </div>
          <Switch
            checked={config.ENABLE_FILE_UPLOAD}
            onCheckedChange={(checked) => handleConfigChange('ENABLE_FILE_UPLOAD', checked)}
          />
        </div>

        {config.ENABLE_FILE_UPLOAD && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="file-size-limit">File Size Limit (MB)</Label>
              <Input
                id="file-size-limit"
                type="number"
                min="1"
                max="1000"
                value={config.FILE_SIZE_LIMIT}
                onChange={(e) => handleConfigChange('FILE_SIZE_LIMIT', parseInt(e.target.value))}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="allowed-file-types">Allowed File Types</Label>
              <Input
                id="allowed-file-types"
                value={config.ALLOWED_FILE_TYPES}
                onChange={(e) => handleConfigChange('ALLOWED_FILE_TYPES', e.target.value)}
                placeholder="pdf,txt,docx,md"
                className="mt-1"
              />
            </div>
          </div>
        )}
      </div>

      <Separator />

      {/* Advanced Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 flex items-center gap-2">
          <Zap className="w-5 h-5" />
          Advanced Settings
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="task-model">Task Model</Label>
            <Input
              id="task-model"
              value={config.TASK_MODEL}
              onChange={(e) => handleConfigChange('TASK_MODEL', e.target.value)}
              placeholder="gpt-3.5-turbo"
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="webhook-url">Webhook URL</Label>
            <Input
              id="webhook-url"
              value={config.WEBHOOK_URL}
              onChange={(e) => handleConfigChange('WEBHOOK_URL', e.target.value)}
              placeholder="https://your-webhook-url.com"
              className="mt-1"
            />
          </div>
        </div>

        <div>
          <Label htmlFor="title-generation-prompt">Title Generation Prompt Template</Label>
          <Textarea
            id="title-generation-prompt"
            value={config.TITLE_GENERATION_PROMPT_TEMPLATE}
            onChange={(e) => handleConfigChange('TITLE_GENERATION_PROMPT_TEMPLATE', e.target.value)}
            placeholder="Create a concise, 3-5 word title for this conversation..."
            className="mt-1"
            rows={3}
          />
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700   ">
        <Button onClick={saveConfig} className="px-6 py-2 bg-white hover:bg-gray-900 text-white dark:bg-white dark:text-black hover:bg-gray-100 transition-colors rounded-lg border border-gray-200 dark:border-gray-700">
          Save Settings
        </Button>
      </div>
    </div>
  );
};
