'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useUIStore, useAuthStore, useAppStore, useChatStore } from '@/lib/stores';

import { Navbar } from './Navbar';
import { Sidebar } from './Sidebar';
import { WebSocketProvider } from '@/components/websocket/WebSocketProvider';
import { cn } from '@/lib/utils';

interface AppLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export const AppLayout: React.FC<AppLayoutProps> = ({ children, className }) => {
  const router = useRouter();
  const pathname = usePathname();
  const { sidebarOpen } = useUIStore();
  const { isAuthenticated, isLoading: authLoading, token } = useAuthStore();
  const { isInitialized, selectedModels, setSelectedModels } = useAppStore();
  const { setCurrentChat } = useChatStore();

  const [currentConversationId, setCurrentConversationId] = useState<string | undefined>(undefined);
  const [hasCheckedAuth, setHasCheckedAuth] = useState(false);

  // Update currentConversationId based on current path
  useEffect(() => {
    const chatIdMatch = pathname.match(/^\/c\/([^/]+)/);
    if (chatIdMatch) {
      const chatId = chatIdMatch[1];
      setCurrentConversationId(chatId);
    } else if (pathname === '/') {
      setCurrentConversationId(undefined);
    }
  }, [pathname]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + O for new chat (matching ifm-chat-feature-k2)
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'O') {
        event.preventDefault();
        handleNewConversation('chat');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [router]);

  // Don't refresh session here - let useAuth hook handle all authentication
  // This prevents duplicate auth API calls
  useEffect(() => {
    if (isAuthenticated && !authLoading && token && isInitialized) {
      // All authentication and initialization is handled by useAuth hook and AppProvider
      // No need to make additional API calls here
    }
  }, [isAuthenticated, authLoading, token, isInitialized]);

  // Handle new conversation creation
  const handleNewConversation = (type?: 'chat' | 'agent') => {
    // Clear current chat state to reset the interface
    setCurrentChat(null);
    setCurrentConversationId(undefined);
    router.push('/');
  };

  // Handle conversation selection
  const handleConversationSelect = (id: string) => {
    setCurrentConversationId(id);
    router.push(`/c/${id}`);
  };

  // Only redirect to auth if we're absolutely sure there's no valid authentication
  useEffect(() => {
    if (hasCheckedAuth) return; // Prevent multiple checks
    
    // Add a delay to ensure auth initialization has completed
    const checkAuth = setTimeout(() => {
      
      if (!authLoading && typeof window !== 'undefined') {
        const storedToken = localStorage.getItem('token');
        
        if (!storedToken && !isAuthenticated) {
          // No token stored and not authenticated, safe to redirect
          setHasCheckedAuth(true);
          router.push('/auth');
        } else if (storedToken || isAuthenticated) {
          // We have a token or are authenticated, don't redirect
          setHasCheckedAuth(true);
        }
        // If there's a stored token, let useAuth hook handle the validation
        // Don't redirect immediately to avoid interrupting the auth initialization
      }
    }, 500); // Increased delay to allow auth state to settle

    return () => clearTimeout(checkAuth);
  }, [isAuthenticated, authLoading, token, router, hasCheckedAuth]);

  // Show loading screen while initializing
  if (!isInitialized || authLoading || !hasCheckedAuth) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 dark:border-white mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  // Render the app if we have a token or are still loading
  // This prevents interrupting the user experience during token validation

  return (
    <WebSocketProvider>
      <div className={cn("min-h-screen bg-gray-50 dark:bg-gray-900 overflow-x-hidden", className)}>
        {/* Sidebar */}
        <Sidebar 
          currentConversationId={currentConversationId}
          onConversationSelect={handleConversationSelect}
          onNewConversation={handleNewConversation}
        />

        {/* Sidebar overlay for mobile - only when expanded */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => useUIStore.getState().setSidebarOpen(false)}
          />
        )}

        {/* Main content area */}
        <div className={cn(
          "flex flex-col min-h-screen transition-all duration-300 ease-in-out",
          sidebarOpen ? "lg:ml-[288px]" : "lg:ml-[48px]"
        )}>
          {/* Navigation bar */}
          {/* <Navbar
            selectedModels={selectedModels}
            onModelsChange={setSelectedModels}
            initNewChat={() => handleNewConversation('chat')}
          /> */}

          {/* Main content */}
          <main className="flex-1 overflow-hidden">
            <div className="h-full">
              {children}
            </div>
          </main>
        </div>
      </div>
    </WebSocketProvider>
  );
};
