'use client';

import React from 'react';
import { useUIStore, useAuthStore } from '@/lib/stores';
import { Button } from '@/components/ui/button';
import { ConversationList } from '@/components/ConversationList';
import { UserMenu } from '@/components/layout/sidebar/UserMenu';
import { cn } from '@/lib/utils';
import { PanelLeft, Plus, MessageSquare, Search } from 'lucide-react';
import { useState, useEffect } from 'react';
import { useChats } from '@/hooks/useChats';
import { useRouter } from 'next/navigation';

interface SidebarProps {
  className?: string;
  currentConversationId?: string;
  onConversationSelect?: (id: string) => void;
  onNewConversation?: (type?: 'chat' | 'agent') => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ 
  className, 
  currentConversationId,
  onConversationSelect = () => {},
  onNewConversation = () => {}
}) => {
  const { sidebarOpen, setSidebarOpen } = useUIStore();
  const { user } = useAuthStore();
  const { chats, isLoading, isInitialized, createChat, loadChats } = useChats();
  const router = useRouter();

  const [search, setSearch] = useState('');
  const [filteredChats, setFilteredChats] = useState(chats);

  // Load chats when user changes or sidebar opens (only if not initialized)
  useEffect(() => {
    if (user && sidebarOpen && !isInitialized && !isLoading) {
      loadChats(1);
    }
  }, [user, sidebarOpen, isInitialized, isLoading, loadChats]); // Use isInitialized instead of chats.length

  // Filter chats based on search
  useEffect(() => {
    if (search.trim()) {
      const filtered = chats.filter(chat =>
        chat.title?.toLowerCase().includes(search.toLowerCase()) ||
        chat.messages?.some(msg =>
          msg.content?.toLowerCase().includes(search.toLowerCase())
        )
      );
      setFilteredChats(filtered);
    } else {
      setFilteredChats(chats);
    }
  }, [search, chats]);

  const handleNewChat = async () => {
    try {
      // First navigate to home page to ensure proper route change
      await router.push('/');
      
      // Small delay to ensure navigation completes
      setTimeout(() => {
        // Then trigger the parent's new conversation handler
        onNewConversation('chat');
      }, 50);
    } catch (error) {
      console.error('Failed to start new chat:', error);
    }
  };

  const handleChatClick = (chatId: string) => {
    // Navigate to chat (will be implemented with router)
  };

  return (
    <div className={cn(
      "fixed inset-y-0 left-0 z-50 bg-[#F3F3F6] transform transition-all duration-300 ease-in-out border-r border-[#E9E9EA]",
      sidebarOpen ? "w-[288px]" : "w-[48px]",
      className
    )}>
      <div className="flex flex-col h-full">
        {sidebarOpen ? (
          <>
            {/* Main content panel - light gray background */}
            <div className="flex-1 mx-[12px] rounded-lg overflow-hidden">
              <div className="flex flex-col h-full">
                {/* Top section with logo and new chat button */}
                <div className="py-[12px] mb-[24px]">
                  <div className="flex items-center justify-between mb-[8px]">
                    {/* Left: Purple logo */}
                    <img src="/static/logo-icon.svg" className="w-[32px] h-[32px]" />
                    {/* Right: Document icon with close button */}
                    <img src="/static/expense-icon.svg" className="w-[18px] h-[18px] cursor-pointer" onClick={() => setSidebarOpen(false)}/>
                    
                  </div>
                  
                  {/* New Chat Button */}
                  <div
                    onClick={handleNewChat}
                    className="w-full bg-[#E8E9FF] flex items-center p-[8px] text-[#625DEC] border border-[#DDDFFF] font-sm font-semibold rounded-lg transition-colors cursor-pointer"
                  >
                    <Plus className="w-[16px] h-[16px] mr-[8px]" />
                    New chat
                  </div>
                </div>

                {/* Recents section */}
                <div className="flex-1 flex flex-col min-h-0">
                  <div className="h-[24px] mb-[4px] px-[8px] flex-shrink-0">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm text-[#8384A3]">Recents</h3>
                      <Search className="w-[16px] h-[16px] text-[#8384A3]" />
                    </div>
                  </div>
                  
                  {/* Conversation List */}
                  <div className="flex-1 min-h-0">
                    <ConversationList
                      currentConversationId={currentConversationId}
                      onConversationSelect={onConversationSelect}
                      onNewConversation={onNewConversation}
                    />
                  </div>
                </div>
              </div>
            </div>
          </>
        ) : (
          /* Collapsed sidebar - only show expand icon */
          <div className="flex flex-col h-full items-center py-[12px]">
            {/* <button
              onClick={() => setSidebarOpen(true)}
              className="p-2 rounded-lg hover:bg-[#E9E9EA] transition-colors"
              title="展开侧边栏"
            >
              <PanelLeft className="w-[20px] h-[20px] text-[#8384A3]" />
            </button> */}
            <img src="/static/logo-icon.svg" className="w-[24px] h-[24px] cursor-pointer" onClick={() => setSidebarOpen(true)}/>
            <Plus className="w-[18px] h-[18px] text-black mt-[32px] cursor-pointer" onClick={handleNewChat}/>
          </div>
        )}

        {/* User Menu at bottom */}
        {/* <div className="p-2">
          <UserMenu />
        </div> */}
      </div>
    </div>
  );
};
