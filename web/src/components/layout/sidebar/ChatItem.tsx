'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tooltip } from '@/components/ui/tooltip';
import { DeleteConfirmModal } from '@/components/ui/modal';
import { RenameModal } from '@/components/ui/RenameModal';
import { 
  MoreVertical, 
  Edit, 
  Copy, 
  Share, 
  Archive, 
  Trash2,
  MessageSquare,
  Pin,
  PinOff,
  Tag,
  Sparkles,
  Check,
  X
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface Chat {
  id: string;
  title: string;
  updated_at: string;
  created_at: string;
  pinned?: boolean;
  archived?: boolean;
  tags?: string[];
  folder_id?: string;
  type?: 'chat' | 'agent';
  chat?: {
    messages?: unknown[];
    models?: string[];
    type?: 'chat' | 'agent';
  };
}

interface ChatItemProps {
  chat: Chat;
  isActive?: boolean;
  isSelected?: boolean;
  shiftKey?: boolean;
  onSelect?: () => void;
  onUnselect?: () => void;
  onRename?: (newTitle: string) => void;
  onDelete?: () => void;
  onClone?: () => void;
  onShare?: () => void;
  onArchive?: () => void;
  onPin?: () => void;
  onTag?: (tag: string) => void;
  onGenerateTitle?: () => Promise<string | null>;
  onChange?: () => void;
  className?: string;
  draggable?: boolean;
  onDragStart?: (e: React.DragEvent) => void;
  onDrag?: (e: React.DragEvent) => void;
  onDragEnd?: (e: React.DragEvent) => void;
}

export const ChatItem: React.FC<ChatItemProps> = ({
  chat,
  isActive = false,
  isSelected = false,
  shiftKey = false,
  onSelect,
  onUnselect,
  onRename,
  onDelete,
  onClone,
  onShare,
  onArchive,
  onPin,
  onTag,
  onGenerateTitle,
  onChange,
  className,
  draggable = false,
  onDragStart,
  onDrag,
  onDragEnd
}) => {
  const router = useRouter();
  const [showRenameModal, setShowRenameModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 });
  const itemRef = useRef<HTMLDivElement>(null);

  // Handle global hover reset event
  useEffect(() => {
    const handleResetHover = () => {
      if (!isActive && !isSelected) {
        setIsHovered(false);
      }
    };

    document.addEventListener('resetChatItemHover', handleResetHover);
    return () => {
      document.removeEventListener('resetChatItemHover', handleResetHover);
    };
  }, [chat.id, isActive, isSelected]);

  const handleClick = () => {
    
    // Determine the chat type - check nested chat.type first, then fallback to top-level type
    const chatType = chat.chat?.type || chat.type || 'chat';
    const navigationUrl = `/c/${chat.id}${chatType === 'agent' ? '?mode=agent' : ''}`;
    
    
    // Reset hover state of other items
    document.dispatchEvent(new CustomEvent('resetChatItemHover'));
    
    router.push(navigationUrl);
    onSelect?.();
  };

  const handleRename = async (newTitle: string) => {
    if (newTitle.trim() && newTitle.trim() !== chat.title) {
      await onRename?.(newTitle.trim());
      onChange?.();
    }
  };

  const handleGenerateTitle = async () => {
    if (!onGenerateTitle) return;
    
    setIsGenerating(true);
    try {
      const generatedTitle = await onGenerateTitle();
      if (generatedTitle && generatedTitle !== chat.title) {
        await onRename?.(generatedTitle);
        onChange?.();
      }
    } catch (error) {
      console.error('Failed to generate title:', error);
    } finally {
      setIsGenerating(false);
    }
  };


  const handleMenuAction = (action: string) => {
    switch (action) {
      case 'rename':
        setShowRenameModal(true);
        break;
      case 'clone':
        onClone?.();
        onChange?.();
        break;
      case 'share':
        onShare?.();
        break;
      case 'archive':
        onArchive?.();
        onChange?.();
        break;
      case 'pin':
        onPin?.();
        onChange?.();
        break;
      case 'delete':
        setShowDeleteConfirm(true);
        break;
    }
  };

  // Drag handlers
  const handleDragStart = useCallback((e: React.DragEvent) => {
    e.stopPropagation();
    
    // Create invisible drag image
    const dragImage = new Image();
    dragImage.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';
    e.dataTransfer.setDragImage(dragImage, 0, 0);
    
    // Set drag data
    e.dataTransfer.setData('text/plain', JSON.stringify({
      type: 'chat',
      id: chat.id,
      item: chat
    }));
    
    setIsDragging(true);
    if (itemRef.current) {
      itemRef.current.style.opacity = '0.5';
    }
    
    onDragStart?.(e);
  }, [chat, onDragStart]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.stopPropagation();
    setDragPosition({ x: e.clientX, y: e.clientY });
    onDrag?.(e);
  }, [onDrag]);

  const handleDragEnd = useCallback((e: React.DragEvent) => {
    e.stopPropagation();
    setIsDragging(false);
    if (itemRef.current) {
      itemRef.current.style.opacity = '1';
    }
    onDragEnd?.(e);
  }, [onDragEnd]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays}d`;
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  return (
    <>
      {/* Drag Ghost */}
      {isDragging && dragPosition.x && dragPosition.y && (
        <div 
          className="fixed pointer-events-none z-50 bg-black/80 backdrop-blur-2xl px-2 py-1 rounded-lg w-fit max-w-40"
          style={{
            left: dragPosition.x + 10,
            top: dragPosition.y - 10
          }}
        >
          <div className="flex items-center gap-1">
            <MessageSquare className="w-[18px] h-[18px]" strokeWidth={2} />
            <div className="text-xs truncate">
              {chat.title}
            </div>
          </div>
        </div>
      )}

      <div
        ref={itemRef}
        draggable={draggable}
        onDragStart={handleDragStart}
        onDrag={handleDrag}
        onDragEnd={handleDragEnd}
        className={cn(
          "group relative p-[8px] cursor-pointer text-[#2E2F41] hover:bg-[#E9E9EA] transition-colors rounded-lg",
          isActive && "bg-[#E9E9EA]",
          isGenerating && "cursor-not-allowed",
          className
        )}
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Chat Title - Simple text style */}
        <div className="flex-1 min-w-0">
          <div className="text-sm truncate w-[calc(100%-24px)]">
            {chat.title || 'New Chat'}
          </div>
        </div>

        {/* Menu - Hidden by default, only show on hover */}
        <div className={cn(
          "absolute right-1 top-1/2 -translate-y-1/2 z-40",
          isActive ? "" : isSelected ? "" : "invisible group-hover:visible"
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={(e) => e.stopPropagation()}
        >
          <div className="flex items-center z-50">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                  <img src="/static/show-more.svg" className='w-[16px] h-[16px]' alt="" onClick={(e) => {
                    e.stopPropagation();
                    onSelect?.();
                  }}/>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[128px] z-[9999] bg-white text-[#2E2F41]">
                <DropdownMenuItem 
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleMenuAction('rename');
                  }} 
                  className="hover:bg-gray-100 cursor-pointer"
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Rename
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleMenuAction('delete');
                  }}
                  className="hover:bg-red-100 hover:text-red-600 cursor-pointer"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      {/* Rename Modal */}
      <RenameModal
        show={showRenameModal}
        onClose={() => setShowRenameModal(false)}
        onConfirm={handleRename}
        currentName={chat.title}
        title="Rename Chat"
        placeholder="Enter new chat name"
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        show={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={() => {
          onDelete?.();
          onChange?.();
          setShowDeleteConfirm(false);
        }}
        title="Delete chat"
        message={`Are you sure you want to delete this chat?`}
        confirmText="Delete"
        cancelText="Cancel"
      />
    </>
  );
};
