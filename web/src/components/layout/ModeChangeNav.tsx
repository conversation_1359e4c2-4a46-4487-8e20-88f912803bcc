'use client';
import { UserMenu } from '@/components/layout/sidebar/UserMenu';


interface ModeChangeNavProps {
  selectedMode: 'chat' | 'agent';
  onModeChange: (mode: 'chat' | 'agent') => void;
}

export default function ModeChangeNav({ selectedMode, onModeChange }: ModeChangeNavProps) {
  return (
    <div className="flex items-center justify-between py-[12px] px-[24px] bg-gray-50">
      {/* Segmented Control */}
      <div className="flex bg-[#F3F3F6] border border-[#E9E9EA] rounded-xl p-[3px] w-[180px]">
        <div
          onClick={() => onModeChange('chat')}
          className={`cursor-pointer text-center leading-[30px] h-[30px] flex-1 rounded-xl transition-all duration-200 ${
            selectedMode === 'chat' 
              ? 'bg-white text-[#2E2F41] font-semibold' 
              : 'text-[#8384A3] hover:text-gray-600'
          }`}
        >
          Chat
        </div>
        <div
          onClick={() => onModeChange('agent')}
          className={`cursor-pointer text-center leading-[30px] h-[30px] flex-1 rounded-xl transition-all duration-200 ${
            selectedMode === 'agent' 
              ? 'bg-white text-[#2E2F41] font-semibold' 
              : 'text-[#8384A3] hover:text-gray-600'
          }`}
        >
          Agent
        </div>
      </div>

      {/* User Avatar */}
      <UserMenu />
    </div>
  );
}
