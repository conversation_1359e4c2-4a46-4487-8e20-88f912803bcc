import { mockUser, mockModels, mockConfig } from './mock-data';

// API错误处理工具
export const handleApiResponse = async (response: Response) => {
  const contentType = response.headers.get('Content-Type');
  
  // 检查是否是JSON响应
  if (!contentType || !contentType.includes('application/json')) {
    // 如果不是JSON，尝试获取HTML内容用于调试
    const htmlContent = await response.text();
    
    // 如果响应是HTML，可能是404页面或服务器错误页面
    if (htmlContent.includes('<!doctype') || htmlContent.includes('<html')) {
      throw new Error(`API_SERVER_UNAVAILABLE: ${response.url}`);
    }
    
    throw new Error(`非JSON响应: ${response.status} ${response.statusText}`);
  }

  // 如果是JSON响应但状态码不正确
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `请求失败: ${response.status} ${response.statusText}`);
  }

  return response.json();
};

// 安全的JSON解析，处理HTML响应，并提供fallback
export const safeJsonParse = async (response: Response, fallbackData?: any) => {
  try {
    return await handleApiResponse(response);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    // 如果是API服务器不可用，并且有fallback数据，则使用fallback
    if (errorMessage.includes('API_SERVER_UNAVAILABLE') && fallbackData !== undefined) {
      console.warn('API服务器不可用，使用模拟数据:', response.url);
      return fallbackData;
    }
    
    console.error('API请求错误:', error);
    throw error;
  }
};

// 根据URL返回对应的模拟数据
export const getMockDataForUrl = (url: string) => {
  if (url.includes('/models')) {
    return mockModels;
  }
  if (url.includes('/config')) {
    return mockConfig;
  }
  if (url.includes('/user') || url.includes('/auth')) {
    return mockUser;
  }
  return null;
};