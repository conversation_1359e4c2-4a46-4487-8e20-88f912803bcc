import { v4 as uuidv4 } from 'uuid';
import type { Message } from '@/lib/types';

// History structure from original project
export interface ChatHistory {
  messages: Record<string, HistoryMessage>;
  currentId: string | null;
}

export interface HistoryMessage {
  id: string;
  parentId: string | null;
  childrenIds: string[];
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
  model?: string;
  modelName?: string;
  modelIdx?: number;
  done?: boolean;
  error?: any;
  files?: any[];
  sources?: any[];
  usage?: any;
  userContext?: string | null;
  statusHistory?: any[];
  code_executions?: any[];
  merged?: any;
  selectedModelId?: string;
  arena?: boolean;
  lastSentence?: string;
  originalContent?: string;
  models?: string[];
  isStreaming?: boolean; // Flag to indicate if content is being streamed
}

// Create messages list from history (recursive traversal)
export const createMessagesList = (history: ChatHistory, messageId: string | null): Message[] => {
  if (messageId === null) {
    return [];
  }

  const message = history.messages[messageId];
  if (!message) {
    return [];
  }

  // Convert HistoryMessage to Message type for component compatibility
  const convertToMessage = (historyMsg: HistoryMessage): Message => ({
    id: historyMsg.id,
    role: historyMsg.role,
    content: historyMsg.content,
    timestamp: historyMsg.timestamp,
    parentId: historyMsg.parentId,
    childrenIds: historyMsg.childrenIds,
    done: historyMsg.done,
    error: historyMsg.error,
    // Historical messages are never streaming, ensure isStreaming is false
    isStreaming: false,
    ...(historyMsg.files && { files: historyMsg.files }),
    ...(historyMsg.model && { model: historyMsg.model }),
    ...(historyMsg.modelName && { modelName: historyMsg.modelName }),
    ...(historyMsg.modelIdx !== undefined && { modelIdx: historyMsg.modelIdx }),
    ...(historyMsg.sources && { sources: historyMsg.sources }),
    ...(historyMsg.usage && { usage: historyMsg.usage }),
    ...(historyMsg.userContext && { userContext: historyMsg.userContext }),
    ...(historyMsg.statusHistory && { statusHistory: historyMsg.statusHistory }),
    ...(historyMsg.code_executions && { code_executions: historyMsg.code_executions }),
    ...(historyMsg.merged && { merged: historyMsg.merged }),
    ...(historyMsg.selectedModelId && { selectedModelId: historyMsg.selectedModelId }),
    ...(historyMsg.arena !== undefined && { arena: historyMsg.arena }),
    ...(historyMsg.lastSentence && { lastSentence: historyMsg.lastSentence }),
    ...(historyMsg.originalContent && { originalContent: historyMsg.originalContent }),
    ...(historyMsg.models && { models: historyMsg.models })
  });

  if (message.parentId) {
    const parentMessages = createMessagesList(history, message.parentId);
    const result = [...parentMessages, convertToMessage(message)];
    return result;
  } else {
    const result = [convertToMessage(message)];
    return result;
  }
};

// Convert messages array to history structure
export const convertMessagesToHistory = (messages: Message[]): ChatHistory => {
  const history: ChatHistory = {
    messages: {},
    currentId: null
  };

  let parentMessageId: string | null = null;
  let messageId: string | null = null;

  for (const message of messages) {
    messageId = uuidv4();

    if (parentMessageId !== null) {
      history.messages[parentMessageId].childrenIds = [
        ...history.messages[parentMessageId].childrenIds,
        messageId
      ];
    }

    history.messages[messageId] = {
      id: messageId,
      parentId: parentMessageId,
      childrenIds: [],
      role: message.role,
      content: message.content,
      timestamp: message.timestamp || Math.floor(Date.now() / 1000),
      ...(message.files && { files: message.files }),
      ...(message.model && { model: message.model }),
      ...(message.modelName && { modelName: message.modelName }),
      done: true
    };

    parentMessageId = messageId;
  }

  history.currentId = messageId;
  return history;
};

// Convert history back to messages array
export const convertHistoryToMessages = (history: ChatHistory): Message[] => {
  if (!history.currentId) {
    return [];
  }

  const messagesList = createMessagesList(history, history.currentId);
  return messagesList.map((msg) => ({
    id: msg.id,
    role: msg.role,
    content: msg.content,
    timestamp: msg.timestamp,
    ...(msg.files && { files: msg.files }),
    ...(msg.model && { model: msg.model }),
    ...(msg.modelName && { modelName: msg.modelName }),
    ...(msg.sources && { sources: msg.sources }),
    ...(msg.usage && { usage: msg.usage })
  }));
};

// Extract message content parts for TTS
export const getMessageContentParts = (content: string, splitOn: string = 'punctuation'): string[] => {
  if (!content || content.trim() === '') {
    return [];
  }

  let parts: string[] = [];
  
  if (splitOn === 'punctuation') {
    // Split on sentence endings
    parts = content.split(/[.!?]+/).filter(part => part.trim().length > 0);
  } else if (splitOn === 'word') {
    // Split on words
    parts = content.split(/\s+/).filter(part => part.trim().length > 0);
  } else {
    // Default: return whole content
    parts = [content];
  }

  return parts.map(part => part.trim()).filter(part => part.length > 0);
};

// Extract sentences for audio
export const extractSentencesForAudio = (content: string): string[] => {
  return getMessageContentParts(content, 'punctuation');
};

// Process details in message content
export const processDetails = (content: string): string => {
  // Remove details tags or process them as needed
  return content;
};

// Remove details from content
export const removeDetails = (content: string): string => {
  return content;
};

// Get prompt variables
export const getPromptVariables = (userName?: string, userLocation?: any) => {
  return {
    user_name: userName || 'User',
    user_location: userLocation || null,
    current_date: new Date().toISOString().split('T')[0],
    current_time: new Date().toLocaleTimeString()
  };
};

// Prompt template processing
export const promptTemplate = (
  template: string,
  userName?: string,
  userLocation?: any
): string => {
  if (!template) return '';

  const variables = getPromptVariables(userName, userLocation);
  
  let processed = template;
  
  // Replace template variables
  Object.entries(variables).forEach(([key, value]) => {
    const regex = new RegExp(`{{${key}}}`, 'g');
    processed = processed.replace(regex, String(value || ''));
  });

  return processed;
};

// Split stream processing
export const splitStream = (content: string, chunkSize: number = 3): string[] => {
  if (content.length <= chunkSize) {
    return [content];
  }

  const chunks: string[] = [];
  for (let i = 0; i < content.length; i += chunkSize) {
    chunks.push(content.slice(i, i + chunkSize));
  }
  
  return chunks;
};

// Sleep utility
export const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Copy to clipboard utility
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const result = document.execCommand('copy');
      textArea.remove();
      return result;
    }
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
};

// Validate chat ID format
export const isValidChatId = (id: string): boolean => {
  if (!id || id === 'new' || id === 'local') return false;
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(id);
};

// Get time range for chat timestamps
export const getTimeRange = (timestamp: number): string => {
  const now = Date.now();
  const diff = now - (timestamp * 1000); // Convert to milliseconds
  
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  
  if (minutes < 1) return 'Just now';
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  if (days < 7) return `${days}d ago`;
  
  const date = new Date(timestamp * 1000);
  return date.toLocaleDateString();
};

export default {
  createMessagesList,
  convertMessagesToHistory,
  convertHistoryToMessages,
  getMessageContentParts,
  extractSentencesForAudio,
  processDetails,
  removeDetails,
  getPromptVariables,
  promptTemplate,
  splitStream,
  sleep,
  copyToClipboard,
  isValidChatId,
  getTimeRange
};