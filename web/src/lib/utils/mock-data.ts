// 模拟数据，用于后端不可用时的fallback
export const mockUser = {
  id: 'mock-user-1',
  name: 'Demo User',
  email: '<EMAIL>',
  role: 'user',
  profile_image_url: null,
  created_at: Date.now(),
  updated_at: Date.now(),
  settings: {}
};

export const mockModels = [
  {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    owned_by: 'openai',
    info: {
      meta: {
        description: 'Fast and efficient model for general conversations',
        parameter_size: '175B',
        capabilities: ['text-generation', 'conversation']
      }
    }
  },
  {
    id: 'gpt-4',
    name: 'GPT-4',
    owned_by: 'openai',
    info: {
      meta: {
        description: 'Most capable model for complex tasks',
        parameter_size: '1.76T',
        capabilities: ['text-generation', 'conversation', 'reasoning']
      }
    }
  },
  {
    id: 'claude-3-sonnet',
    name: 'Claude 3 Sonnet',
    owned_by: 'anthropic',
    info: {
      meta: {
        description: 'Balanced model for various tasks',
        parameter_size: '200B',
        capabilities: ['text-generation', 'conversation', 'analysis']
      }
    }
  }
];

export const mockConfig = {
  features: {
    enable_signup: true,
    enable_ldap: false,
    enable_oauth: false,
    enable_web_search: true,
    enable_image_generation: false
  },
  default_models: ['gpt-3.5-turbo'],
  default_prompt_suggestions: [
    {
      title: '👋 你好',
      content: '你好！有什么我可以帮助你的吗？'
    },
    {
      title: '💡 解释概念',
      content: '请解释一下...'
    },
    {
      title: '✍️ 写代码',
      content: '帮我写一个...'
    }
  ]
};