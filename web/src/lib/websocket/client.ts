import { io, Socket } from 'socket.io-client';
import { WEBUI_API_BASE_URL } from '@/lib/constants';

export interface WebSocketConfig {
  enableWebsocket?: boolean;
  token?: string;
}

export interface SocketEventHandlers {
  onConnect?: () => void;
  onDisconnect?: (reason: string) => void;
  onConnectError?: (error: Error) => void;
  onReconnect?: (attemptNumber: number) => void;
  onReconnectError?: (error: Error) => void;
  onReconnectFailed?: () => void;
  onUserList?: (data: { user_ids: string[] }) => void;
  onUsage?: (data: { models: string[] }) => void;
  onChatEvents?: (data: any) => void;
  onChannelEvents?: (data: any) => void;
}

export class WebSocketClient {
  private socket: Socket | null = null;
  public config: WebSocketConfig = {}; // 改为public以便外部访问
  private handlers: SocketEventHandlers = {};
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor(config: WebSocketConfig = {}) {
    this.config = config;
  }

  connect(token?: string): Promise<Socket> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve(this.socket);
        return;
      }

      const authToken = token || this.config.token;
      if (!authToken) {
        reject(new Error('No authentication token provided'));
        return;
      }

      try {
        // 构建Socket.IO连接 - 与原项目完全一致的配置
        const baseUrl = WEBUI_API_BASE_URL.replace('/api/v1', '');
        
        
        this.socket = io(baseUrl, {
          reconnection: true,
          reconnectionDelay: 1000,
          reconnectionDelayMax: 5000,
          randomizationFactor: 0.5,
          path: '/ws/socket.io',
          transports: this.config.enableWebsocket ? ['websocket'] : ['polling', 'websocket'],
          auth: { token: authToken }
          // 移除timeout，使用默认值与原项目一致
        });

        this.setupEventListeners();

        this.socket.on('connect', () => {
          this.reconnectAttempts = 0;
          this.handlers.onConnect?.();
          resolve(this.socket!);
        });

        this.socket.on('connect_error', (error) => {
          console.error('❌ Socket.IO connection error:', error);
          console.error('This likely means the backend does not have Socket.IO support enabled');
          console.error('Backend may be missing WebSocket/Socket.IO configuration');
          this.handlers.onConnectError?.(error);
          reject(new Error(`Socket.IO connection failed: ${error.message}. Backend may not support WebSocket.`));
        });

        // Set up a timeout to catch if connection never happens
        const connectionTimeout = setTimeout(() => {
          // console.error('⏰ Socket.IO connection timeout - backend may not support WebSocket');
          // reject(new Error('Socket.IO connection timeout - backend may not support WebSocket'));
        }, 15000);

        this.socket.on('connect', () => {
          clearTimeout(connectionTimeout);
        });

        this.socket.on('connect_error', () => {
          clearTimeout(connectionTimeout);
        });

      } catch (error) {
        console.error('Failed to create Socket.IO connection:', error);
        reject(error);
      }
    });
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  getSocket(): Socket | null {
    return this.socket;
  }

  emit(event: string, data: any): void {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    } else {
      console.warn('Socket.IO not connected, cannot emit event:', event);
    }
  }

  on(event: string, handler: (...args: any[]) => void): void {
    if (this.socket) {
      this.socket.on(event, handler);
    }
  }

  off(event: string, handler?: (...args: any[]) => void): void {
    if (this.socket) {
      this.socket.off(event, handler);
    }
  }

  setEventHandlers(handlers: SocketEventHandlers): void {
    this.handlers = { ...this.handlers, ...handlers };
  }

  private setupEventListeners(): void {
    if (!this.socket) return;

    this.socket.on('disconnect', (reason, details) => {
      this.handlers.onDisconnect?.(reason);
    });

    this.socket.on('reconnect_attempt', (attemptNumber) => {
      this.reconnectAttempts = attemptNumber;
    });

    this.socket.on('reconnect', (attemptNumber) => {
      this.reconnectAttempts = 0;
      this.handlers.onReconnect?.(attemptNumber);
    });

    this.socket.on('reconnect_error', (error) => {
      console.error('Socket.IO reconnect error:', error);
      this.handlers.onReconnectError?.(error);
    });

    this.socket.on('reconnect_failed', () => {
      console.error('Socket.IO reconnect failed after', this.maxReconnectAttempts, 'attempts');
      this.handlers.onReconnectFailed?.();
    });

    // Application-specific events
    this.socket.on('user-list', (data) => {
      this.handlers.onUserList?.(data);
    });

    this.socket.on('usage', (data) => {
      this.handlers.onUsage?.(data);
    });

    this.socket.on('chat-events', (data) => {
      this.handlers.onChatEvents?.(data);
    });

    this.socket.on('channel-events', (data) => {
      this.handlers.onChannelEvents?.(data);
    });
  }

  // Convenience methods for common operations
  joinChannel(channelId: string): void {
    this.emit('join-channel', { channel_id: channelId });
  }

  leaveChannel(channelId: string): void {
    this.emit('leave-channel', { channel_id: channelId });
  }

  // Send chat message
  async sendChatMessage(data: {
    chatId: string;
    messageId: string;
    message: any;
    models: string[];
    files?: any[];
  }): Promise<any> {
    if (!this.socket?.connected) {
      throw new Error('WebSocket not connected');
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Socket timeout'));
      }, 30000); // 30 second timeout

      this.socket!.emit('chat', {
        chat_id: data.chatId,
        message_id: data.messageId,
        message: data.message,
        models: data.models,
        files: data.files || []
      }, (response: any) => {
        clearTimeout(timeout);

        if (response?.error) {
          reject(new Error(response.error));
        } else {
          resolve(response);
        }
      });
    });
  }

  // Subscribe to chat events with specific handler
  onChatEvent(handler: (data: any) => void): void {
    if (!this.socket) return;

    // Listen for chat-events which contain structured event data
    this.socket.on('chat-events', handler);
  }

  // Remove chat event listeners
  offChatEvent(handler?: (data: any) => void): void {
    if (!this.socket) return;

    if (handler) {
      this.socket.off('chat-events', handler);
    } else {
      this.socket.off('chat-events');
    }
  }

  sendTyping(channelId: string, threadId?: string): void {
    this.emit('channel-events', {
      channel_id: channelId,
      message_id: threadId,
      data: { type: 'typing' }
    });
  }

  sendUsage(modelId: string): void {
    this.emit('usage', { model: modelId });
  }

  // Join user session for chat events
  joinUserSession(token: string): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.socket?.connected) {
        reject(new Error('Socket not connected'));
        return;
      }

      const timeout = setTimeout(() => {
        reject(new Error('User join timeout'));
      }, 10000);

      this.socket.emit('user-join', {
        auth: { token }
      }, (response: any) => {
        clearTimeout(timeout);
        
        if (response?.error) {
          reject(new Error(response.error));
        } else {
          resolve(response);
        }
      });
    });
  }
}

// Singleton instance
let webSocketClient: WebSocketClient | null = null;

export const getWebSocketClient = (): WebSocketClient => {
  if (!webSocketClient) {
    webSocketClient = new WebSocketClient();
  }
  return webSocketClient;
};

export const createWebSocketClient = (config: WebSocketConfig): WebSocketClient => {
  webSocketClient = new WebSocketClient(config);
  return webSocketClient;
};