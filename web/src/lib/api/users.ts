import { WEBUI_API_BASE_URL } from '@/lib/constants';
import type { User, UserSettings } from '@/lib/types';

// Get user settings
export const getUserSettings = async (token: string): Promise<UserSettings> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/user/settings`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get user settings:', error);
    throw error;
  }
};

// Update user settings
export const updateUserSettings = async (
  token: string,
  settings: any
): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/user/settings/update`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bear<PERSON> ${token}`
      },
      body: JSON.stringify(settings)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw errorData;
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Failed to update user settings:', error);
    throw error;
  }
};

// Get user by ID
export const getUserById = async (token: string, userId: string): Promise<User> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/${userId}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get user by ID:', error);
    throw error;
  }
};

// Get all users (admin only)
export const getUsers = async (token: string): Promise<User[]> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get users:', error);
    throw error;
  }
};

// Update user by ID (admin only)
export const updateUserById = async (
  token: string,
  userId: string,
  userData: Partial<User>
): Promise<User> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/${userId}/update`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify(userData)
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to update user:', error);
    throw error;
  }
};

// Delete user by ID (admin only)
export const deleteUserById = async (token: string, userId: string): Promise<void> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/${userId}`, {
      method: 'DELETE',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }
  } catch (error) {
    console.error('Failed to delete user:', error);
    throw error;
  }
};

// Get user permissions
export const getUserPermissions = async (token: string, userId: string): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/${userId}/permissions`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get user permissions:', error);
    throw error;
  }
};

// Update user permissions (admin only)
export const updateUserPermissions = async (
  token: string,
  userId: string,
  permissions: any
): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/${userId}/permissions`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify(permissions)
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to update user permissions:', error);
    throw error;
  }
};

// Get user location and update
export const getAndUpdateUserLocation = async (token: string): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/user/location`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get and update user location:', error);
    throw error;
  }
};

// Export user data
export const exportUserData = async (token: string): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/user/export`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to export user data:', error);
    throw error;
  }
};

// Delete user account
export const deleteUserAccount = async (token: string): Promise<void> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/user/delete`, {
      method: 'DELETE',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }
  } catch (error) {
    console.error('Failed to delete user account:', error);
    throw error;
  }
};

// Get user info by session user
export const getUserInfo = async (token: string): Promise<User> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/user/info`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get user info:', error);
    throw error;
  }
};

// Update user info
export const updateUserInfo = async (
  token: string,
  userInfo: Partial<User>
): Promise<User> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/user/info/update`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify(userInfo)
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to update user info:', error);
    throw error;
  }
};

// Get active users
export const getActiveUsers = async (token: string): Promise<string[]> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/active`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    const data = await response.json();
    return data.user_ids || [];
  } catch (error) {
    console.error('Failed to get active users:', error);
    throw error;
  }
};

// Get all users (admin only) - alternative implementation with query support
export const getAllUsers = async (
  token: string,
  query?: string,
  orderBy?: string,
  direction?: string,
  page = 1
): Promise<User[]> => {
  try {
    const searchParams = new URLSearchParams();
    searchParams.set('page', `${page}`);

    if (query) {
      searchParams.set('query', query);
    }

    if (orderBy) {
      searchParams.set('order_by', orderBy);
    }

    if (direction) {
      searchParams.set('direction', direction);
    }

    const response = await fetch(`${WEBUI_API_BASE_URL}/users/all?${searchParams.toString()}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get all users:', error);
    throw error;
  }
};

// Get user groups
export const getUserGroups = async (token: string): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/groups`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get user groups:', error);
    throw error;
  }
};

// Get user default permissions
export const getUserDefaultPermissions = async (token: string): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/default/permissions`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get user default permissions:', error);
    throw error;
  }
};

// Update user default permissions
export const updateUserDefaultPermissions = async (
  token: string,
  permissions: any
): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/default/permissions`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify(permissions)
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to update user default permissions:', error);
    throw error;
  }
};

// Update user role
export const updateUserRole = async (
  token: string,
  userId: string,
  role: string
): Promise<any> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/users/update/role`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify({
        id: userId,
        role: role
      })
    });

    if (!response.ok) {
      throw await response.json();
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to update user role:', error);
    throw error;
  }
};
