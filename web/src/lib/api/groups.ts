import { WEBUI_API_BASE_URL } from '@/lib/constants';

export interface Group {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  user_count: number;
  created_at: number;
}

export interface CreateGroupRequest {
  name: string;
  description: string;
  permissions?: Record<string, any>;
}

export interface UpdateGroupRequest {
  name?: string;
  description?: string;
  permissions?: Record<string, any>;
}

// Get all groups
export const getGroups = async (token: string): Promise<Group[]> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/groups/`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    const groups = await response.json();
    
    // Transform backend response to match frontend interface
    return groups.map((group: any) => ({
      id: group.id || '',
      name: group.name || '',
      description: group.description || '',
      permissions: group.permissions ? Object.keys(group.permissions) : [],
      user_count: group.user_ids ? group.user_ids.length : 0,
      created_at: group.created_at ? group.created_at * 1000 : Date.now() // Convert from seconds to milliseconds
    }));
  } catch (error) {
    console.error('Failed to get groups:', error);
    throw error;
  }
};

// Get group by ID
export const getGroupById = async (token: string, id: string): Promise<Group> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/groups/id/${id}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }

    const group = await response.json();
    
    return {
      id: group.id || '',
      name: group.name || '',
      description: group.description || '',
      permissions: group.permissions ? Object.keys(group.permissions) : [],
      user_count: group.user_ids ? group.user_ids.length : 0,
      created_at: group.created_at ? group.created_at * 1000 : Date.now()
    };
  } catch (error) {
    console.error('Failed to get group by ID:', error);
    throw error;
  }
};

// Create new group
export const createNewGroup = async (token: string, group: CreateGroupRequest): Promise<Group> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/groups/create`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify(group)
    });

    if (!response.ok) {
      throw await response.json();
    }

    const newGroup = await response.json();
    
    return {
      id: newGroup.id || '',
      name: newGroup.name || '',
      description: newGroup.description || '',
      permissions: newGroup.permissions ? Object.keys(newGroup.permissions) : [],
      user_count: newGroup.user_ids ? newGroup.user_ids.length : 0,
      created_at: newGroup.created_at ? newGroup.created_at * 1000 : Date.now()
    };
  } catch (error) {
    console.error('Failed to create group:', error);
    throw error;
  }
};

// Update group by ID
export const updateGroupById = async (token: string, id: string, group: UpdateGroupRequest): Promise<Group> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/groups/id/${id}/update`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      },
      body: JSON.stringify(group)
    });

    if (!response.ok) {
      throw await response.json();
    }

    const updatedGroup = await response.json();
    
    return {
      id: updatedGroup.id || '',
      name: updatedGroup.name || '',
      description: updatedGroup.description || '',
      permissions: updatedGroup.permissions ? Object.keys(updatedGroup.permissions) : [],
      user_count: updatedGroup.user_ids ? updatedGroup.user_ids.length : 0,
      created_at: updatedGroup.created_at ? updatedGroup.created_at * 1000 : Date.now()
    };
  } catch (error) {
    console.error('Failed to update group:', error);
    throw error;
  }
};

// Delete group by ID
export const deleteGroupById = async (token: string, id: string): Promise<void> => {
  try {
    const response = await fetch(`${WEBUI_API_BASE_URL}/groups/id/${id}/delete`, {
      method: 'DELETE',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        authorization: `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw await response.json();
    }
  } catch (error) {
    console.error('Failed to delete group:', error);
    throw error;
  }
};
