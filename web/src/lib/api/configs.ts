import { apiClient } from './index';
import { WEBUI_BASE_URL } from '@/lib/constants';

// Config Types
export interface ConfigResponse {
  [key: string]: any;
}

export interface Banner {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  content: string;
  dismissible: boolean;
  timestamp: number;
}

// Import/Export Config
export const importConfig = async (token: string, config: any): Promise<ConfigResponse> => {
  try {
    const response = await apiClient.post('/configs/import', { config }, token);
    return response;
  } catch (error) {
    console.error('Failed to import config:', error);
    throw error;
  }
};

export const exportConfig = async (token: string): Promise<ConfigResponse> => {
  try {
    const response = await apiClient.get('/configs/export', token);
    return response;
  } catch (error) {
    console.error('Failed to export config:', error);
    throw error;
  }
};

// Direct Connections Config
export const getDirectConnectionsConfig = async (token: string): Promise<ConfigResponse> => {
  try {
    const response = await apiClient.get('/configs/direct_connections', token);
    return response;
  } catch (error) {
    console.error('Failed to get direct connections config:', error);
    throw error;
  }
};

export const setDirectConnectionsConfig = async (token: string, config: any): Promise<ConfigResponse> => {
  try {
    const response = await apiClient.post('/configs/direct_connections', config, token);
    return response;
  } catch (error) {
    console.error('Failed to set direct connections config:', error);
    throw error;
  }
};

// Tool Server Connections
export const getToolServerConnections = async (token: string): Promise<ConfigResponse> => {
  try {
    const response = await apiClient.get('/configs/tool_servers', token);
    return response;
  } catch (error) {
    console.error('Failed to get tool server connections:', error);
    throw error;
  }
};

export const setToolServerConnections = async (token: string, connections: any): Promise<ConfigResponse> => {
  try {
    const response = await apiClient.post('/configs/tool_servers', connections, token);
    return response;
  } catch (error) {
    console.error('Failed to set tool server connections:', error);
    throw error;
  }
};

export const verifyToolServerConnection = async (token: string, connection: any): Promise<ConfigResponse> => {
  try {
    const response = await apiClient.post('/configs/tool_servers/verify', connection, token);
    return response;
  } catch (error) {
    console.error('Failed to verify tool server connection:', error);
    throw error;
  }
};

// Code Execution Config
export const getCodeExecutionConfig = async (token: string): Promise<ConfigResponse> => {
  try {
    const response = await apiClient.get('/configs/code_execution', token);
    return response;
  } catch (error) {
    console.error('Failed to get code execution config:', error);
    throw error;
  }
};

export const setCodeExecutionConfig = async (token: string, config: any): Promise<ConfigResponse> => {
  try {
    const response = await apiClient.post('/configs/code_execution', config, token);
    return response;
  } catch (error) {
    console.error('Failed to set code execution config:', error);
    throw error;
  }
};

// Models Config
export const getModelsConfig = async (token: string): Promise<ConfigResponse> => {
  try {
    const response = await apiClient.get('/api/config/models', token);
    return response;
  } catch (error) {
    console.error('Failed to get models config:', error);
    throw error;
  }
};

export const setModelsConfig = async (token: string, config: any): Promise<ConfigResponse> => {
  try {
    const response = await apiClient.post('/api/config/models', config, token);
    return response;
  } catch (error) {
    console.error('Failed to set models config:', error);
    throw error;
  }
};

// Default Prompt Suggestions
export const setDefaultPromptSuggestions = async (token: string, promptSuggestions: string): Promise<ConfigResponse> => {
  try {
    const response = await apiClient.post('/configs/suggestions', { suggestions: promptSuggestions }, token);
    return response;
  } catch (error) {
    console.error('Failed to set default prompt suggestions:', error);
    throw error;
  }
};

// Banners
export const getBanners = async (token: string): Promise<Banner[]> => {
  try {
    const response = await apiClient.get('/configs/banners', token);
    return response;
  } catch (error) {
    console.error('Failed to get banners:', error);
    throw error;
  }
};

export const setBanners = async (token: string, banners: Banner[]): Promise<ConfigResponse> => {
  try {
    const response = await apiClient.post('/configs/banners', { banners }, token);
    return response;
  } catch (error) {
    console.error('Failed to set banners:', error);
    throw error;
  }
};

// General Application Config (from backend main API)
export const getAppConfig = async (): Promise<ConfigResponse> => {
  try {
    const response = await fetch(`${WEBUI_BASE_URL}/api/config`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      console.warn(`Failed to get config: HTTP ${response.status}`);
      // Return fallback config
      return {
        name: 'Open WebUI',
        version: '0.3.32',
        features: {
          enable_signup: true,
          enable_login_form: true,
          enable_message_rating: true,
          enable_community_sharing: false
        },
        default_models: [],
        default_prompt_suggestions: []
      };
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get app config:', error);
    // Return fallback config
    return {
      name: 'Open WebUI',
      version: '0.3.32',
      features: {
        enable_signup: true,
        enable_login_form: true,
        enable_message_rating: true,
        enable_community_sharing: false
      },
      default_models: [],
      default_prompt_suggestions: []
    };
  }
};

// Webhook URL
export const getWebhookUrl = async (token: string): Promise<{ url: string }> => {
  try {
    const response = await fetch(`${WEBUI_BASE_URL}/api/webhook`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      console.warn(`Failed to get webhook URL: HTTP ${response.status}`);
      return { url: '' };
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get webhook URL:', error);
    return { url: '' };
  }
};

export const setWebhookUrl = async (token: string, url: string): Promise<{ url: string }> => {
  try {
    const response = await fetch(`${WEBUI_BASE_URL}/api/webhook`, {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ url })
    });

    if (!response.ok) {
      console.warn(`Failed to set webhook URL: HTTP ${response.status}`);
      return { url };
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to set webhook URL:', error);
    return { url };
  }
};

// Version info
export const getVersion = async (): Promise<{ version: string }> => {
  try {
    const response = await fetch(`${WEBUI_BASE_URL}/api/version`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      console.warn(`Failed to get version: HTTP ${response.status}`);
      return { version: '0.3.32' };
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get version:', error);
    return { version: '0.3.32' };
  }
};