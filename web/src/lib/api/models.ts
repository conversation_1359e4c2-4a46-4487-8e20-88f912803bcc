import { apiClient } from './index';
import { safeJsonParse, getMockDataForUrl } from '@/lib/utils/api-error-handler';

export interface Model {
  id: string;
  name: string;
  provider?: string;
  base_url?: string;
  api_key?: string;
  enabled?: boolean;
  visible?: boolean;
  temperature?: number;
  max_tokens?: number;
  description?: string;
  capabilities?: string[];
  pricing?: {
    input: number;
    output: number;
  };
  info?: any;
  owned_by?: string;
  pipe?: any;
  preset?: any;
  urlIdx?: number;
}

export interface ModelResponse {
  data: Model[];
}

// Get all models
export const getModels = async (token: string): Promise<ModelResponse> => {
  try {
    const response = await fetch('/api/models', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    const mockData = getMockDataForUrl(response.url);
    const data = await safeJsonParse(response, mockData);
    
    // 如果数据是数组格式，包装成ModelResponse格式
    if (Array.isArray(data)) {
      return { data };
    }
    
    return data;
  } catch (error) {
    console.error('Failed to get models:', error);
    // 如果所有都失败了，返回模拟数据
    const mockData = getMockDataForUrl('/api/models');
    return { data: mockData || [] };
  }
};

// Get base models (admin only)
export const getBaseModels = async (token: string): Promise<ModelResponse> => {
  try {
    const response = await fetch('/api/v1/models/base', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Failed to get base models:', error);
    throw error;
  }
};

// Create new model
export const createNewModel = async (token: string, model: Partial<Model>): Promise<Model> => {
  try {
    const response = await apiClient.post('/api/v1/models/create', model, token);
    return response;
  } catch (error) {
    console.error('Failed to create new model:', error);
    throw error;
  }
};

// Get model by ID
export const getModelById = async (token: string, id: string): Promise<Model> => {
  try {
    const response = await apiClient.get(`/api/v1/models/model?id=${encodeURIComponent(id)}`, token);
    return response;
  } catch (error) {
    console.error('Failed to get model by ID:', error);
    throw error;
  }
};

// Update model
export const updateModel = async (token: string, id: string, model: Partial<Model>): Promise<Model> => {
  try {
    const response = await apiClient.post(`/api/v1/models/model/update?id=${encodeURIComponent(id)}`, model, token);
    return response;
  } catch (error) {
    console.error('Failed to update model:', error);
    throw error;
  }
};

// Delete model
export const deleteModel = async (token: string, id: string): Promise<boolean> => {
  try {
    const response = await apiClient.delete(`/api/v1/models/model/delete?id=${encodeURIComponent(id)}`, token);
    return response;
  } catch (error) {
    console.error('Failed to delete model:', error);
    throw error;
  }
};

// Get model configurations from backend API
export const getModelsConfig = async (token: string): Promise<any> => {
  try {
    const response = await apiClient.get('/api/config/models', token);
    return response;
  } catch (error) {
    console.error('Failed to get models config:', error);
    throw error;
  }
};

// Set model configurations
export const setModelsConfig = async (token: string, config: any): Promise<any> => {
  try {
    const response = await apiClient.post('/api/config/models', config, token);
    return response;
  } catch (error) {
    console.error('Failed to set models config:', error);
    throw error;
  }
};