import { apiClient } from './index';
import type { User, Chat, Model } from '@/lib/types';

// Admin User Management
export const getAdminUsers = async (token: string, page: number = 1, limit: number = 50, query?: string, orderBy?: string, direction?: string): Promise<{
  users: User[];
  total: number;
  page: number;
  pages: number;
}> => {
  try {
    const searchParams = new URLSearchParams();
    searchParams.set('page', page.toString());
    if (query) searchParams.set('query', query);
    if (orderBy) searchParams.set('order_by', orderBy);
    if (direction) searchParams.set('direction', direction);
    
    const response = await apiClient.get(`/users/?${searchParams.toString()}`, token);
    
    return {
      users: response.users || [],
      total: response.total || 0,
      page: response.page || page,
      pages: response.pages || Math.ceil((response.total || 0) / limit)
    };
  } catch (error) {
    console.error('Failed to get admin users:', error);
    throw error;
  }
};

export const createAdminUser = async (token: string, userData: {
  email: string;
  name: string;
  password: string;
  role: 'admin' | 'user';
}): Promise<User> => {
  try {
    // Create user via auth API
    const response = await apiClient.post('/auths/signup', {
      email: userData.email,
      name: userData.name,
      password: userData.password
    });
    
    // If role is admin, update the role
    if (userData.role === 'admin' && response.id) {
      await apiClient.post('/users/update/role', {
        id: response.id,
        role: 'admin'
      }, token);
      response.role = 'admin';
    }
    
    return response;
  } catch (error) {
    console.error('Failed to create admin user:', error);
    throw error;
  }
};

export const updateAdminUser = async (token: string, userId: string, updates: Partial<User>): Promise<User> => {
  try {
    // Update user info if basic info changed
    if (updates.name || updates.email || updates.profile_image_url) {
      await apiClient.post(`/users/${userId}/update`, {
        name: updates.name,
        email: updates.email,
        profile_image_url: updates.profile_image_url,
        password: ''
      }, token);
    }
    
    // Update user role if changed
    if (updates.role) {
      await apiClient.post('/users/update/role', {
        id: userId,
        role: updates.role
      }, token);
    }
    
    // Return updated user data
    const updatedUser = await apiClient.get(`/users/${userId}`, token);
    return updatedUser;
  } catch (error) {
    console.error('Failed to update admin user:', error);
    throw error;
  }
};

export const deleteAdminUser = async (token: string, userId: string): Promise<void> => {
  try {
    await apiClient.delete(`/users/${userId}`, token);
  } catch (error) {
    console.error('Failed to delete admin user:', error);
    throw error;
  }
};

// Admin System Statistics
export interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  totalChats: number;
  totalModels: number;
  totalFunctions: number;
  systemHealth: 'Healthy' | 'Warning' | 'Critical';
  resourceUsage: {
    cpu: number;
    memory: number;
    storage: number;
  };
}

export const getAdminStats = async (token: string): Promise<AdminStats> => {
  try {
    // Gather stats from multiple APIs
    const [usersResponse, modelsResponse, functionsResponse] = await Promise.allSettled([
      apiClient.get('/users/all', token),
      apiClient.get('/models', token),
      apiClient.get('/functions/', token)
    ]);
    
    const totalUsers = usersResponse.status === 'fulfilled' ? (usersResponse.value?.users?.length || 0) : 0;
    const totalModels = modelsResponse.status === 'fulfilled' ? (Array.isArray(modelsResponse.value?.data) ? modelsResponse.value.data.length : 0) : 0;
    const totalFunctions = functionsResponse.status === 'fulfilled' ? (Array.isArray(functionsResponse.value) ? functionsResponse.value.length : 0) : 0;
    
    // Get active users count
    let activeUsers = 0;
    try {
      const activeUsersResponse = await apiClient.get('/users/active', token);
      activeUsers = activeUsersResponse?.user_ids?.length || 0;
    } catch (error) {
      console.warn('Failed to get active users count:', error);
    }
    
    // TODO: Get real chat count from chats API when available
    const totalChats = 0; // Placeholder
    
    return {
      totalUsers,
      activeUsers,
      totalChats,
      totalModels,
      totalFunctions,
      systemHealth: 'Healthy', // TODO: Implement real health check
      resourceUsage: {
        cpu: 0, // TODO: Get from system metrics API
        memory: 0, // TODO: Get from system metrics API
        storage: 0 // TODO: Get from system metrics API
      }
    };
  } catch (error) {
    console.error('Failed to get admin stats:', error);
    // Return basic stats on error
    return {
      totalUsers: 0,
      activeUsers: 0,
      totalChats: 0,
      totalModels: 0,
      totalFunctions: 0,
      systemHealth: 'Warning',
      resourceUsage: {
        cpu: 0,
        memory: 0,
        storage: 0
      }
    };
  }
};

// Admin Functions Management
export interface AdminFunction {
  id: string;
  name: string;
  description: string;
  author: string;
  version: string;
  status: 'active' | 'inactive' | 'error';
  type: 'builtin' | 'custom' | 'community';
  category: string;
  tags: string[];
  usage_count: number;
  created_at: number;
  updated_at: number;
  code?: string;
  parameters?: any[];
}

export const getAdminFunctions = async (token: string, page: number = 1, limit: number = 20): Promise<{
  functions: AdminFunction[];
  total: number;
  page: number;
  pages: number;
}> => {
  try {
    const response = await apiClient.get('/functions/', token);
    
    const functions = Array.isArray(response) ? response : [];
    const total = functions.length;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedFunctions = functions.slice(startIndex, endIndex);
    
    // Transform the response to match AdminFunction interface
    const adminFunctions: AdminFunction[] = paginatedFunctions.map((func: any) => ({
      id: func.id,
      name: func.name || 'Unknown Function',
      description: func.description || '',
      author: func.meta?.author || 'Unknown',
      version: func.meta?.version || '1.0.0',
      status: func.is_active ? 'active' : 'inactive',
      type: func.type || 'custom',
      category: func.meta?.category || 'General',
      tags: func.meta?.tags || [],
      usage_count: func.usage_count || 0,
      created_at: func.created_at ? new Date(func.created_at).getTime() : Date.now(),
      updated_at: func.updated_at ? new Date(func.updated_at).getTime() : Date.now(),
      code: func.content,
      parameters: func.specs
    }));
    
    return {
      functions: adminFunctions,
      total,
      page,
      pages: Math.ceil(total / limit)
    };
  } catch (error) {
    console.error('Failed to get admin functions:', error);
    throw error;
  }
};

export const createAdminFunction = async (token: string, functionData: Omit<AdminFunction, 'id' | 'created_at' | 'updated_at' | 'usage_count'>): Promise<AdminFunction> => {
  try {
    const funcPayload = {
      name: functionData.name,
      content: functionData.code || '',
      description: functionData.description,
      type: functionData.type,
      meta: {
        author: functionData.author,
        version: functionData.version,
        category: functionData.category,
        tags: functionData.tags
      },
      specs: functionData.parameters || []
    };
    
    const response = await apiClient.post('/functions/create', funcPayload, token);
    
    // Transform response back to AdminFunction format
    return {
      id: response.id,
      name: response.name,
      description: response.description,
      author: response.meta?.author || functionData.author,
      version: response.meta?.version || functionData.version,
      status: response.is_active ? 'active' : 'inactive',
      type: response.type,
      category: response.meta?.category || functionData.category,
      tags: response.meta?.tags || functionData.tags,
      usage_count: 0,
      created_at: Date.now(),
      updated_at: Date.now(),
      code: response.content,
      parameters: response.specs
    };
  } catch (error) {
    console.error('Failed to create function:', error);
    throw error;
  }
};

export const updateAdminFunction = async (token: string, functionId: string, updates: Partial<AdminFunction>): Promise<AdminFunction> => {
  try {
    const updatePayload: any = {};
    
    if (updates.name) updatePayload.name = updates.name;
    if (updates.description) updatePayload.description = updates.description;
    if (updates.code !== undefined) updatePayload.content = updates.code;
    if (updates.type) updatePayload.type = updates.type;
    if (updates.parameters) updatePayload.specs = updates.parameters;
    
    if (updates.author || updates.version || updates.category || updates.tags) {
      updatePayload.meta = {
        ...(updates.author && { author: updates.author }),
        ...(updates.version && { version: updates.version }),
        ...(updates.category && { category: updates.category }),
        ...(updates.tags && { tags: updates.tags })
      };
    }
    
    const response = await apiClient.post(`/functions/id/${functionId}/update`, updatePayload, token);
    
    // Transform response back to AdminFunction format
    return {
      id: response.id,
      name: response.name,
      description: response.description,
      author: response.meta?.author || updates.author || '',
      version: response.meta?.version || updates.version || '1.0.0',
      status: response.is_active ? 'active' : 'inactive',
      type: response.type,
      category: response.meta?.category || updates.category || 'General',
      tags: response.meta?.tags || updates.tags || [],
      usage_count: response.usage_count || 0,
      created_at: response.created_at ? new Date(response.created_at).getTime() : Date.now(),
      updated_at: Date.now(),
      code: response.content,
      parameters: response.specs
    };
  } catch (error) {
    console.error('Failed to update function:', error);
    throw error;
  }
};

export const deleteAdminFunction = async (token: string, functionId: string): Promise<void> => {
  try {
    await apiClient.delete(`/functions/id/${functionId}/delete`, token);
  } catch (error) {
    console.error('Failed to delete function:', error);
    throw error;
  }
};

// Admin Evaluations Management
export interface AdminEvaluation {
  id: string;
  model_id: string;
  model_name: string;
  score: number;
  response_count: number;
  trend: 'up' | 'down' | 'stable';
  metrics: {
    accuracy: number;
    helpfulness: number;
    response_time: number;
    quality: number;
  };
  created_at: number;
  updated_at: number;
}

export interface AdminFeedback {
  id: string;
  user_id: string;
  user_name: string;
  model_id: string;
  model_name: string;
  rating: number;
  comment: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  created_at: number;
}

export const getAdminEvaluations = async (token: string): Promise<AdminEvaluation[]> => {
  try {
    // Note: There's no backend endpoint for evaluations yet, so return empty array
    // The backend only has /evaluations/config and /evaluations/feedbacks/all
    console.log('Admin evaluations endpoint not implemented yet, returning empty array');
    return [];
  } catch (error) {
    console.error('Failed to get admin evaluations:', error);
    // Return empty array on error - evaluations may not be implemented yet
    return [];
  }
};

export const getAdminFeedback = async (token: string, page: number = 1, limit: number = 20): Promise<{
  feedback: AdminFeedback[];
  total: number;
  page: number;
  pages: number;
}> => {
  try {
    // Use the correct endpoint that exists in the backend
    const response: any = await apiClient.get('/evaluations/feedbacks/all', token);

    // The backend returns an array of feedbacks directly
    const allFeedbacks = Array.isArray(response) ? response : [];

    // Apply pagination manually since the backend endpoint doesn't support it
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedFeedbacks = allFeedbacks.slice(startIndex, endIndex);

    const feedback = paginatedFeedbacks.map((fb: any) => ({
      id: fb.id || '',
      user_id: fb.user_id || '',
      user_name: fb.user?.name || fb.user_name || 'Unknown User',
      model_id: fb.model_id || '',
      model_name: fb.model_name || fb.model_id || '',
      rating: fb.rating || 0,
      comment: fb.comment || '',
      sentiment: fb.sentiment || 'neutral',
      created_at: fb.created_at ? new Date(fb.created_at).getTime() : Date.now()
    }));

    const total = allFeedbacks.length;
    const pages = Math.ceil(total / limit);

    return {
      feedback,
      total,
      page,
      pages
    };
  } catch (error) {
    console.error('Failed to get admin feedback:', error);
    // Return empty feedback on error
    return {
      feedback: [],
      total: 0,
      page: 1,
      pages: 1
    };
  }
};

// Admin Settings Management
export interface AdminConfig {
  general: {
    site_name: string;
    site_description: string;
    default_locale: string;
    timezone: string;
  };
  security: {
    enable_registration: boolean;
    enable_ldap: boolean;
    session_timeout: number;
    max_login_attempts: number;
  };
  features: {
    enable_thinking: boolean;
    enable_functions: boolean;
    enable_evaluations: boolean;
    enable_web_search: boolean;
  };
  limits: {
    max_users: number;
    max_chats_per_user: number;
    max_message_length: number;
    rate_limit_per_minute: number;
  };
}

export const getAdminConfig = async (token: string): Promise<AdminConfig> => {
  try {
    // Get backend configuration
    const backendConfig = await apiClient.get('/config', token);
    
    // Transform backend config to match AdminConfig interface
    return {
      general: {
        site_name: backendConfig.WEBUI_NAME || 'AI Assistant',
        site_description: backendConfig.WEBUI_DESCRIPTION || 'Advanced AI Chat Interface',
        default_locale: backendConfig.DEFAULT_LOCALE || 'en-US',
        timezone: backendConfig.TIMEZONE || 'UTC'
      },
      security: {
        enable_registration: backendConfig.enable_signup !== false,
        enable_ldap: backendConfig.features?.enable_ldap === true,
        session_timeout: backendConfig.SESSION_TIMEOUT || 24,
        max_login_attempts: backendConfig.MAX_LOGIN_ATTEMPTS || 5
      },
      features: {
        enable_thinking: backendConfig.features?.enable_thinking !== false,
        enable_functions: backendConfig.features?.enable_functions !== false,
        enable_evaluations: backendConfig.features?.enable_evaluations !== false,
        enable_web_search: backendConfig.enable_web_search === true
      },
      limits: {
        max_users: backendConfig.MAX_USERS || 1000,
        max_chats_per_user: backendConfig.MAX_CHATS_PER_USER || 100,
        max_message_length: backendConfig.MAX_MESSAGE_LENGTH || 8000,
        rate_limit_per_minute: backendConfig.RATE_LIMIT_PER_MINUTE || 60
      }
    };
  } catch (error) {
    console.error('Failed to get admin config:', error);
    // Return default config on error
    return {
      general: {
        site_name: 'AI Assistant',
        site_description: 'Advanced AI Chat Interface',
        default_locale: 'en-US',
        timezone: 'UTC'
      },
      security: {
        enable_registration: true,
        enable_ldap: false,
        session_timeout: 24,
        max_login_attempts: 5
      },
      features: {
        enable_thinking: true,
        enable_functions: true,
        enable_evaluations: true,
        enable_web_search: false
      },
      limits: {
        max_users: 1000,
        max_chats_per_user: 100,
        max_message_length: 8000,
        rate_limit_per_minute: 60
      }
    };
  }
};

export const updateAdminConfig = async (token: string, config: Partial<AdminConfig>): Promise<AdminConfig> => {
  try {
    // Transform AdminConfig format to backend config format
    const backendConfig: any = {};
    
    if (config.general) {
      if (config.general.site_name) backendConfig.WEBUI_NAME = config.general.site_name;
      if (config.general.site_description) backendConfig.WEBUI_DESCRIPTION = config.general.site_description;
      if (config.general.default_locale) backendConfig.DEFAULT_LOCALE = config.general.default_locale;
      if (config.general.timezone) backendConfig.TIMEZONE = config.general.timezone;
    }
    
    if (config.security) {
      if (config.security.enable_registration !== undefined) backendConfig.enable_signup = config.security.enable_registration;
      if (config.security.session_timeout) backendConfig.SESSION_TIMEOUT = config.security.session_timeout;
      if (config.security.max_login_attempts) backendConfig.MAX_LOGIN_ATTEMPTS = config.security.max_login_attempts;
    }
    
    if (config.features) {
      backendConfig.features = {
        ...(config.features.enable_ldap !== undefined && { enable_ldap: config.features.enable_ldap }),
        ...(config.features.enable_thinking !== undefined && { enable_thinking: config.features.enable_thinking }),
        ...(config.features.enable_functions !== undefined && { enable_functions: config.features.enable_functions }),
        ...(config.features.enable_evaluations !== undefined && { enable_evaluations: config.features.enable_evaluations })
      };
      if (config.features.enable_web_search !== undefined) backendConfig.enable_web_search = config.features.enable_web_search;
    }
    
    if (config.limits) {
      if (config.limits.max_users) backendConfig.MAX_USERS = config.limits.max_users;
      if (config.limits.max_chats_per_user) backendConfig.MAX_CHATS_PER_USER = config.limits.max_chats_per_user;
      if (config.limits.max_message_length) backendConfig.MAX_MESSAGE_LENGTH = config.limits.max_message_length;
      if (config.limits.rate_limit_per_minute) backendConfig.RATE_LIMIT_PER_MINUTE = config.limits.rate_limit_per_minute;
    }
    
    // Update config via backend API
    await apiClient.post('/configs/update', backendConfig, token);
    
    // Return updated config
    return await getAdminConfig(token);
  } catch (error) {
    console.error('Failed to update admin config:', error);
    throw error;
  }
};

// Admin Activity Logs
export interface AdminActivity {
  id: string;
  user_id?: string;
  user_name?: string;
  action: string;
  target_type: 'user' | 'function' | 'config' | 'system';
  target_id?: string;
  details: string;
  ip_address?: string;
  user_agent?: string;
  created_at: number;
}

export const getAdminActivities = async (token: string, page: number = 1, limit: number = 50): Promise<{
  activities: AdminActivity[];
  total: number;
  page: number;
  pages: number;
}> => {
  try {
    // Try to get activity logs from backend
    const searchParams = new URLSearchParams();
    searchParams.set('page', page.toString());
    searchParams.set('limit', limit.toString());
    
    const response = await apiClient.get(`/logs/activities?${searchParams.toString()}`, token);
    
    const activities = Array.isArray(response.activities) ? response.activities.map((activity: any) => ({
      id: activity.id || '',
      user_id: activity.user_id,
      user_name: activity.user_name || activity.user?.name || 'Unknown User',
      action: activity.action || '',
      target_type: activity.target_type || 'system',
      target_id: activity.target_id,
      details: activity.details || activity.description || '',
      ip_address: activity.ip_address,
      user_agent: activity.user_agent,
      created_at: activity.created_at ? new Date(activity.created_at).getTime() : Date.now()
    })) : [];
    
    return {
      activities,
      total: response.total || activities.length,
      page: response.page || page,
      pages: response.pages || Math.ceil((response.total || activities.length) / limit)
    };
  } catch (error) {
    console.error('Failed to get admin activities:', error);
    // Return empty activities on error - activity logging may not be implemented yet
    return {
      activities: [],
      total: 0,
      page: 1,
      pages: 1
    };
  }
};

// Admin Model Management
export const getAdminModels = async (token: string): Promise<Model[]> => {
  try {
    return await apiClient.get('/admin/models', token);
  } catch (error) {
    console.error('Failed to get admin models:', error);
    throw error;
  }
};

export const updateAdminModel = async (token: string, modelId: string, updates: {
  enabled?: boolean;
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
}): Promise<Model> => {
  try {
    return await apiClient.put(`/admin/models/${modelId}`, updates, token);
  } catch (error) {
    console.error('Failed to update model:', error);
    throw error;
  }
};

// Admin Database Operations
export const performDatabaseBackup = async (token: string): Promise<{ backup_id: string; file_path: string }> => {
  try {
    const response = await apiClient.post('/database/backup', {}, token);
    return {
      backup_id: response.id || response.backup_id || '',
      file_path: response.file_path || response.path || ''
    };
  } catch (error) {
    console.error('Failed to perform database backup:', error);
    throw error;
  }
};

export const getDatabaseBackups = async (token: string): Promise<Array<{
  id: string;
  filename: string;
  size: number;
  created_at: number;
}>> => {
  try {
    const response = await apiClient.get('/database/backups', token);
    
    if (Array.isArray(response)) {
      return response.map((backup: any) => ({
        id: backup.id || '',
        filename: backup.filename || backup.name || '',
        size: backup.size || 0,
        created_at: backup.created_at ? new Date(backup.created_at).getTime() : Date.now()
      }));
    }
    
    return [];
  } catch (error) {
    console.error('Failed to get database backups:', error);
    // Return empty array on error - database management may not be implemented yet
    return [];
  }
};

export const restoreDatabaseBackup = async (token: string, backupId: string): Promise<void> => {
  try {
    await apiClient.post(`/database/restore/${backupId}`, {}, token);
  } catch (error) {
    console.error('Failed to restore database backup:', error);
    throw error;
  }
};