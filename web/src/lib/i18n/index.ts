// Simple i18n implementation for the admin panel
export interface I18nTranslations {
  [key: string]: string | I18nTranslations;
}

export interface I18nConfig {
  locale: string;
  fallback: string;
  translations: Record<string, I18nTranslations>;
}

// Default translations
export const defaultTranslations: Record<string, I18nTranslations> = {
  en: {
    // Common
    'Save': 'Save',
    'Cancel': 'Cancel',
    'Delete': 'Delete',
    'Edit': 'Edit',
    'Create': 'Create',
    'Back': 'Back',
    'Loading': 'Loading',
    'Search': 'Search',
    'Settings': 'Settings',
    'Overview': 'Overview',
    'Groups': 'Groups',
    'Name': 'Name',
    'Description': 'Description',
    'Status': 'Status',
    'Actions': 'Actions',
    'Active': 'Active',
    'Inactive': 'Inactive',
    'Error': 'Error',
    'Success': 'Success',
    'Warning': 'Warning',
    'Info': 'Info',
    
    // Admin Panel
    'Admin Panel': 'Admin Panel',
    'Users': 'Users',
    'Evaluations': 'Evaluations',
    'Functions': 'Functions',

    // Evaluations
    'Leaderboard': 'Leaderboard',
    'Feedbacks': 'Feedbacks',
    'Model': 'Model',
    'Rating': 'Rating',
    'Battles': 'Battles',
    'Win Rate': 'Win Rate',
    'User': 'User',
    'Comment': 'Comment',
    'Updated': 'Updated',
    'Export': 'Export',
    'Search models...': 'Search models...',
    'No models found': 'No models found',
    'No feedbacks found': 'No feedbacks found',
    'Help us create the best community leaderboard by sharing your feedback history!': 'Help us create the best community leaderboard by sharing your feedback history!',
    'Feedback deleted successfully': 'Feedback deleted successfully',
    'Failed to delete feedback': 'Failed to delete feedback',
    'Feedbacks exported successfully': 'Feedbacks exported successfully',
    'Failed to export feedbacks': 'Failed to export feedbacks',
    
    // Functions
    'New Function': 'New Function',
    'Create Function': 'Create Function',
    'Edit Function': 'Edit Function',
    'Function Details': 'Function Details',
    'Function ID': 'Function ID',
    'Function Name': 'Function Name',
    'Function Code': 'Function Code',
    'Configuration': 'Configuration',
    'Requirements': 'Requirements',
    'Environment Variables': 'Environment Variables',
    'Test': 'Test',
    'Clone': 'Clone',
    'Import': 'Import',
    'Export': 'Export',
    'Enable': 'Enable',
    'Disable': 'Disable',
    'Share': 'Share',
    'Copy': 'Copy',
    
    // User Management
    'User List': 'User List',
    'Add User': 'Add User',
    'Edit User': 'Edit User',
    'User Groups': 'User Groups',
    'Add Group': 'Add Group',
    'Edit Group': 'Edit Group',
    'Permissions': 'Permissions',
    
    // Messages
    'Are you sure?': 'Are you sure?',
    'This action cannot be undone': 'This action cannot be undone',
    'Successfully created': 'Successfully created',
    'Successfully updated': 'Successfully updated',
    'Successfully deleted': 'Successfully deleted',
    'Failed to create': 'Failed to create',
    'Failed to update': 'Failed to update',
    'Failed to delete': 'Failed to delete',
    'Please fix the validation errors': 'Please fix the validation errors',
    'ID cannot be changed': 'ID cannot be changed',
    'Unsaved changes': 'Unsaved changes',
    
    // Validation
    'This field is required': 'This field is required',
    'Invalid format': 'Invalid format',
    'Function ID can only contain letters, numbers, hyphens, and underscores': 'Function ID can only contain letters, numbers, hyphens, and underscores',
  },
  
  'zh-CN': {
    // Common
    'Save': '保存',
    'Cancel': '取消',
    'Delete': '删除',
    'Edit': '编辑',
    'Create': '创建',
    'Back': '返回',
    'Loading': '加载中',
    'Search': '搜索',
    'Settings': '设置',
    'Overview': '概览',
    'Groups': '分组',
    'Name': '名称',
    'Description': '描述',
    'Status': '状态',
    'Actions': '操作',
    'Active': '激活',
    'Inactive': '停用',
    'Error': '错误',
    'Success': '成功',
    'Warning': '警告',
    'Info': '信息',
    
    // Admin Panel
    'Admin Panel': '管理面板',
    'Users': '用户',
    'Evaluations': '评估',
    'Functions': '函数',

    // Evaluations
    'Leaderboard': '排行榜',
    'Feedbacks': '反馈',
    'Model': '模型',
    'Rating': '评分',
    'Battles': '对战',
    'Win Rate': '胜率',
    'User': '用户',
    'Comment': '评论',
    'Updated': '更新时间',
    'Export': '导出',
    'Search models...': '搜索模型...',
    'No models found': '未找到模型',
    'No feedbacks found': '未找到反馈',
    'Help us create the best community leaderboard by sharing your feedback history!': '通过分享您的反馈历史帮助我们创建最佳的社区排行榜！',
    'Feedback deleted successfully': '反馈删除成功',
    'Failed to delete feedback': '删除反馈失败',
    'Feedbacks exported successfully': '反馈导出成功',
    'Failed to export feedbacks': '导出反馈失败',
    
    // Functions
    'New Function': '新建函数',
    'Create Function': '创建函数',
    'Edit Function': '编辑函数',
    'Function Details': '函数详情',
    'Function ID': '函数ID',
    'Function Name': '函数名称',
    'Function Code': '函数代码',
    'Configuration': '配置',
    'Requirements': '依赖',
    'Environment Variables': '环境变量',
    'Test': '测试',
    'Clone': '克隆',
    'Import': '导入',
    'Export': '导出',
    'Enable': '启用',
    'Disable': '禁用',
    'Share': '分享',
    'Copy': '复制',
    
    // User Management
    'User List': '用户列表',
    'Add User': '添加用户',
    'Edit User': '编辑用户',
    'User Groups': '用户组',
    'Add Group': '添加分组',
    'Edit Group': '编辑分组',
    'Permissions': '权限',
    
    // Messages
    'Are you sure?': '确定吗？',
    'This action cannot be undone': '此操作无法撤销',
    'Successfully created': '创建成功',
    'Successfully updated': '更新成功',
    'Successfully deleted': '删除成功',
    'Failed to create': '创建失败',
    'Failed to update': '更新失败',
    'Failed to delete': '删除失败',
    'Please fix the validation errors': '请修复验证错误',
    'ID cannot be changed': 'ID 无法修改',
    'Unsaved changes': '未保存的更改',
    
    // Validation
    'This field is required': '此字段为必填项',
    'Invalid format': '格式无效',
    'Function ID can only contain letters, numbers, hyphens, and underscores': '函数ID只能包含字母、数字、连字符和下划线',
  }
};

class I18n {
  private locale: string = 'en';
  private fallback: string = 'en';
  private translations: Record<string, I18nTranslations> = defaultTranslations;

  constructor(config?: Partial<I18nConfig>) {
    if (config) {
      this.locale = config.locale || this.locale;
      this.fallback = config.fallback || this.fallback;
      this.translations = { ...this.translations, ...config.translations };
    }
  }

  setLocale(locale: string) {
    this.locale = locale;
  }

  getLocale(): string {
    return this.locale;
  }

  t(key: string, params?: Record<string, string>): string {
    const translation = this.getTranslation(key, this.locale) || 
                       this.getTranslation(key, this.fallback) || 
                       key;
    
    if (params && typeof translation === 'string') {
      return this.interpolate(translation, params);
    }
    
    return typeof translation === 'string' ? translation : key;
  }

  private getTranslation(key: string, locale: string): string | I18nTranslations | undefined {
    const keys = key.split('.');
    let current: string | I18nTranslations | undefined = this.translations[locale];
    
    for (const k of keys) {
      if (typeof current === 'object' && current !== null) {
        current = current[k];
      } else {
        return undefined;
      }
    }
    
    return current;
  }

  private interpolate(template: string, params: Record<string, string>): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return params[key] || match;
    });
  }

  addTranslations(locale: string, translations: I18nTranslations) {
    if (!this.translations[locale]) {
      this.translations[locale] = {};
    }
    this.translations[locale] = { ...this.translations[locale], ...translations };
  }
}

// Create global i18n instance
export const i18n = new I18n();

// Hook for using i18n in React components
export const useI18n = () => {
  return {
    t: (key: string, params?: Record<string, string>) => i18n.t(key, params),
    locale: i18n.getLocale(),
    setLocale: (locale: string) => i18n.setLocale(locale),
  };
};