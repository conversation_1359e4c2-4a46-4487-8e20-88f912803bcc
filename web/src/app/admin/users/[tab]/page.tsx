'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useAuthStore } from '@/lib/stores';
import { Card } from '@/components/ui/card';
import { Shield } from 'lucide-react';

// Import user management pages
import AdminUsersPage from '../page';
import AdminUsersOverviewPage from '../overview/page';
import AdminUsersGroupsPage from '../groups/page';

const VALID_TABS = ['overview', 'users', 'groups'] as const;
type TabType = typeof VALID_TABS[number];

export default function AdminUsersTabPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuthStore();

  const tab = params.tab as string;
  const currentTab: TabType = VALID_TABS.includes(tab as TabType) ? tab as TabType : 'users';

  // Redirect if invalid tab
  useEffect(() => {
    if (tab && !VALID_TABS.includes(tab as TabType)) {
      router.replace('/admin/users');
    }
  }, [tab, router]);

  // Check admin permissions
  if (!user || user.role !== 'admin') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="p-8 text-center">
          <Shield className="w-16 h-16 mx-auto mb-4 text-red-500" />
          <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
          <p className="text-gray-600 dark:text-gray-400">
            You need admin privileges to access this page.
          </p>
        </Card>
      </div>
    );
  }

  // Render the appropriate component based on tab
  switch (currentTab) {
    case 'overview':
      return <AdminUsersOverviewPage />;
    case 'groups':
      return <AdminUsersGroupsPage />;
    case 'users':
    default:
      return <AdminUsersPage />;
  }
}