'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/lib/stores';
import { ArrowLeft, Save, Play, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

// Code editor component (placeholder - you might want to use Monaco Editor or similar)
const CodeEditor: React.FC<{
  value: string;
  onChange: (value: string) => void;
  language?: string;
  className?: string;
}> = ({ value, onChange, language = 'python', className }) => {
  return (
    <div className={cn("relative", className)}>
      <div className="absolute top-2 right-2 text-xs text-gray-500 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
        {language}
      </div>
      <Textarea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="font-mono text-sm min-h-[300px] resize-y"
        placeholder={`# Enter your ${language} code here...
def main(*args, **kwargs):
    """
    This is a sample function.
    """
    return "Hello, World!"
`}
      />
    </div>
  );
};

export default function CreateFunctionPage() {
  const { user } = useAuthStore();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    id: '',
    name: '',
    description: '',
    content: '',
    meta: {
      manifest: {
        requirements: '',
        environment_variables: '',
      }
    }
  });

  // Validation errors
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (user?.role !== 'admin') {
      router.push('/');
      return;
    }
    setIsLoaded(true);
  }, [user, router]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.id.trim()) {
      newErrors.id = 'Function ID is required';
    } else if (!/^[a-zA-Z0-9_-]+$/.test(formData.id)) {
      newErrors.id = 'Function ID can only contain letters, numbers, hyphens, and underscores';
    }

    if (!formData.name.trim()) {
      newErrors.name = 'Function name is required';
    }

    if (!formData.content.trim()) {
      newErrors.content = 'Function code is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fix the validation errors');
      return;
    }

    setLoading(true);
    
    try {
      // TODO: Implement API call to create function
      const response = await fetch('/api/functions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user?.token || ''}`,
        },
        body: JSON.stringify({
          ...formData,
          meta: {
            ...formData.meta,
            manifest: {
              requirements: formData.meta.manifest.requirements.split('\n').filter(r => r.trim()),
              environment_variables: formData.meta.manifest.environment_variables
                ? JSON.parse(formData.meta.manifest.environment_variables)
                : {}
            }
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create function');
      }

      toast.success('Function created successfully');
      router.push('/admin/functions');
    } catch (error) {
      console.error('Error creating function:', error);
      toast.error('Failed to create function');
    } finally {
      setLoading(false);
    }
  };

  const handleTest = async () => {
    if (!formData.content.trim()) {
      toast.error('Please enter function code first');
      return;
    }

    setLoading(true);
    
    try {
      // TODO: Implement API call to test function
      const response = await fetch('/api/functions/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user?.token || ''}`,
        },
        body: JSON.stringify({
          content: formData.content
        }),
      });

      if (!response.ok) {
        throw new Error('Function test failed');
      }

      const result = await response.json();
      toast.success('Function test completed successfully');
      
      // You might want to show the test result in a modal or sidebar
      console.log('Test result:', result);
    } catch (error) {
      console.error('Error testing function:', error);
      toast.error('Function test failed');
    } finally {
      setLoading(false);
    }
  };

  if (!isLoaded) {
    return null;
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/admin/functions')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Functions
          </Button>
          <div className="w-px h-6 bg-gray-300 dark:bg-gray-600" />
          <h1 className="text-2xl font-semibold">Create Function</h1>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleTest}
            disabled={loading || !formData.content.trim()}
            className="flex items-center gap-2"
          >
            <Play className="w-4 h-4" />
            Test
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <Save className="w-4 h-4" />
            {loading ? 'Creating...' : 'Create Function'}
          </Button>
        </div>
      </div>

      <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-6 overflow-hidden">
        {/* Function Details */}
        <div className="lg:col-span-1 space-y-6 overflow-y-auto">
          <Card>
            <CardHeader>
              <CardTitle>Function Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="id">Function ID *</Label>
                <Input
                  id="id"
                  value={formData.id}
                  onChange={(e) => setFormData({ ...formData, id: e.target.value })}
                  placeholder="my_function"
                  className={errors.id ? 'border-red-500' : ''}
                />
                {errors.id && (
                  <p className="text-sm text-red-500 mt-1">{errors.id}</p>
                )}
              </div>

              <div>
                <Label htmlFor="name">Function Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="My Function"
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && (
                  <p className="text-sm text-red-500 mt-1">{errors.name}</p>
                )}
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Describe what this function does..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="requirements">Requirements (one per line)</Label>
                <Textarea
                  id="requirements"
                  value={formData.meta.manifest.requirements}
                  onChange={(e) => setFormData({
                    ...formData,
                    meta: {
                      ...formData.meta,
                      manifest: {
                        ...formData.meta.manifest,
                        requirements: e.target.value
                      }
                    }
                  })}
                  placeholder="requests==2.28.1
numpy>=1.21.0"
                  rows={4}
                />
              </div>

              <div>
                <Label htmlFor="env_vars">Environment Variables (JSON)</Label>
                <Textarea
                  id="env_vars"
                  value={formData.meta.manifest.environment_variables}
                  onChange={(e) => setFormData({
                    ...formData,
                    meta: {
                      ...formData.meta,
                      manifest: {
                        ...formData.meta.manifest,
                        environment_variables: e.target.value
                      }
                    }
                  })}
                  placeholder='{"API_KEY": "your_api_key_here"}'
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Code Editor */}
        <div className="lg:col-span-2 flex flex-col">
          <Card className="flex-1 flex flex-col">
            <CardHeader>
              <CardTitle>Function Code</CardTitle>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col">
              <CodeEditor
                value={formData.content}
                onChange={(value) => setFormData({ ...formData, content: value })}
                className="flex-1"
              />
              {errors.content && (
                <p className="text-sm text-red-500 mt-2">{errors.content}</p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}