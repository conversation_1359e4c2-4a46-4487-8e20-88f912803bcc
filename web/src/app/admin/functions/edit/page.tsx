'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthStore } from '@/lib/stores';
import { ArrowLeft, Save, Play, Trash2, Copy, History } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

// Code editor component (same as create page)
const CodeEditor: React.FC<{
  value: string;
  onChange: (value: string) => void;
  language?: string;
  className?: string;
}> = ({ value, onChange, language = 'python', className }) => {
  return (
    <div className={cn("relative", className)}>
      <div className="absolute top-2 right-2 text-xs text-gray-500 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
        {language}
      </div>
      <Textarea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="font-mono text-sm min-h-[300px] resize-y"
        placeholder={`# Enter your ${language} code here...`}
      />
    </div>
  );
};

interface FunctionData {
  id: string;
  name: string;
  description: string;
  content: string;
  meta: {
    manifest: {
      requirements: string;
      environment_variables: string;
    }
  };
  created_at?: string;
  updated_at?: string;
}

export default function EditFunctionPage() {
  const { user } = useAuthStore();
  const router = useRouter();
  const searchParams = useSearchParams();
  const functionId = searchParams.get('id');
  
  const [loading, setLoading] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [originalData, setOriginalData] = useState<FunctionData | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // Form state
  const [formData, setFormData] = useState<FunctionData>({
    id: '',
    name: '',
    description: '',
    content: '',
    meta: {
      manifest: {
        requirements: '',
        environment_variables: '',
      }
    }
  });

  // Validation errors
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (user?.role !== 'admin') {
      router.push('/');
      return;
    }

    if (!functionId) {
      toast.error('Function ID is required');
      router.push('/admin/functions');
      return;
    }

    loadFunction();
  }, [user, router, functionId]);

  // Check for changes
  useEffect(() => {
    if (originalData) {
      setHasChanges(JSON.stringify(formData) !== JSON.stringify(originalData));
    }
  }, [formData, originalData]);

  const loadFunction = async () => {
    if (!functionId) return;

    setLoading(true);
    try {
      // TODO: Implement API call to load function
      const response = await fetch(`/api/functions/${functionId}`, {
        headers: {
          'Authorization': `Bearer ${user?.token || ''}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to load function');
      }

      const data = await response.json();
      
      // Convert requirements array to string
      const functionData: FunctionData = {
        ...data,
        meta: {
          ...data.meta,
          manifest: {
            requirements: Array.isArray(data.meta?.manifest?.requirements) 
              ? data.meta.manifest.requirements.join('\n')
              : data.meta?.manifest?.requirements || '',
            environment_variables: typeof data.meta?.manifest?.environment_variables === 'object'
              ? JSON.stringify(data.meta.manifest.environment_variables, null, 2)
              : data.meta?.manifest?.environment_variables || ''
          }
        }
      };

      setFormData(functionData);
      setOriginalData(functionData);
      setIsLoaded(true);
    } catch (error) {
      console.error('Error loading function:', error);
      toast.error('Failed to load function');
      router.push('/admin/functions');
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Function name is required';
    }

    if (!formData.content.trim()) {
      newErrors.content = 'Function code is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fix the validation errors');
      return;
    }

    setLoading(true);
    
    try {
      // TODO: Implement API call to update function
      const response = await fetch(`/api/functions/${functionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user?.token || ''}`,
        },
        body: JSON.stringify({
          ...formData,
          meta: {
            ...formData.meta,
            manifest: {
              requirements: formData.meta.manifest.requirements.split('\n').filter(r => r.trim()),
              environment_variables: formData.meta.manifest.environment_variables
                ? JSON.parse(formData.meta.manifest.environment_variables)
                : {}
            }
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update function');
      }

      toast.success('Function updated successfully');
      // Reload the function to get the latest data
      await loadFunction();
    } catch (error) {
      console.error('Error updating function:', error);
      toast.error('Failed to update function');
    } finally {
      setLoading(false);
    }
  };

  const handleTest = async () => {
    if (!formData.content.trim()) {
      toast.error('Please enter function code first');
      return;
    }

    setLoading(true);
    
    try {
      // TODO: Implement API call to test function
      const response = await fetch('/api/functions/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user?.token || ''}`,
        },
        body: JSON.stringify({
          content: formData.content
        }),
      });

      if (!response.ok) {
        throw new Error('Function test failed');
      }

      const result = await response.json();
      toast.success('Function test completed successfully');
      console.log('Test result:', result);
    } catch (error) {
      console.error('Error testing function:', error);
      toast.error('Function test failed');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!functionId) return;

    setLoading(true);
    
    try {
      // TODO: Implement API call to delete function
      const response = await fetch(`/api/functions/${functionId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${user?.token || ''}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete function');
      }

      toast.success('Function deleted successfully');
      router.push('/admin/functions');
    } catch (error) {
      console.error('Error deleting function:', error);
      toast.error('Failed to delete function');
    } finally {
      setLoading(false);
    }
  };

  const handleClone = () => {
    // Navigate to create page with current function data as template
    const queryParams = new URLSearchParams({
      template: JSON.stringify({
        name: `${formData.name} (Copy)`,
        description: formData.description,
        content: formData.content,
        meta: formData.meta
      })
    });
    
    router.push(`/admin/functions/create?${queryParams.toString()}`);
  };

  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p>Loading function...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/admin/functions')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Functions
          </Button>
          <div className="w-px h-6 bg-gray-300 dark:bg-gray-600" />
          <div>
            <h1 className="text-2xl font-semibold">Edit Function</h1>
            <p className="text-sm text-gray-500">ID: {formData.id}</p>
          </div>
          {hasChanges && (
            <div className="px-2 py-1 bg-orange-100 dark:bg-orange-900 text-orange-700 dark:text-orange-300 text-xs rounded">
              Unsaved changes
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleTest}
            disabled={loading || !formData.content.trim()}
            className="flex items-center gap-2"
          >
            <Play className="w-4 h-4" />
            Test
          </Button>
          
          <Button
            variant="outline"
            onClick={handleClone}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <Copy className="w-4 h-4" />
            Clone
          </Button>

          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="outline"
                disabled={loading}
                className="flex items-center gap-2 text-red-600 hover:text-red-700"
              >
                <Trash2 className="w-4 h-4" />
                Delete
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete Function</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete this function? This action cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
          
          <Button
            onClick={handleSubmit}
            disabled={loading || !hasChanges}
            className="flex items-center gap-2"
          >
            <Save className="w-4 h-4" />
            {loading ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-6 overflow-hidden">
        {/* Function Details */}
        <div className="lg:col-span-1 space-y-6 overflow-y-auto">
          <Card>
            <CardHeader>
              <CardTitle>Function Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="id">Function ID</Label>
                <Input
                  id="id"
                  value={formData.id}
                  disabled
                  className="bg-gray-50 dark:bg-gray-800"
                />
                <p className="text-xs text-gray-500 mt-1">ID cannot be changed</p>
              </div>

              <div>
                <Label htmlFor="name">Function Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="My Function"
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && (
                  <p className="text-sm text-red-500 mt-1">{errors.name}</p>
                )}
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Describe what this function does..."
                  rows={3}
                />
              </div>

              {(formData.created_at || formData.updated_at) && (
                <div className="text-xs text-gray-500 space-y-1">
                  {formData.created_at && (
                    <p>Created: {new Date(formData.created_at).toLocaleString()}</p>
                  )}
                  {formData.updated_at && (
                    <p>Updated: {new Date(formData.updated_at).toLocaleString()}</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="requirements">Requirements (one per line)</Label>
                <Textarea
                  id="requirements"
                  value={formData.meta.manifest.requirements}
                  onChange={(e) => setFormData({
                    ...formData,
                    meta: {
                      ...formData.meta,
                      manifest: {
                        ...formData.meta.manifest,
                        requirements: e.target.value
                      }
                    }
                  })}
                  placeholder="requests==2.28.1
numpy>=1.21.0"
                  rows={4}
                />
              </div>

              <div>
                <Label htmlFor="env_vars">Environment Variables (JSON)</Label>
                <Textarea
                  id="env_vars"
                  value={formData.meta.manifest.environment_variables}
                  onChange={(e) => setFormData({
                    ...formData,
                    meta: {
                      ...formData.meta,
                      manifest: {
                        ...formData.meta.manifest,
                        environment_variables: e.target.value
                      }
                    }
                  })}
                  placeholder='{"API_KEY": "your_api_key_here"}'
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Code Editor */}
        <div className="lg:col-span-2 flex flex-col">
          <Card className="flex-1 flex flex-col">
            <CardHeader>
              <CardTitle>Function Code</CardTitle>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col">
              <CodeEditor
                value={formData.content}
                onChange={(value) => setFormData({ ...formData, content: value })}
                className="flex-1"
              />
              {errors.content && (
                <p className="text-sm text-red-500 mt-2">{errors.content}</p>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}