'use client';

import React, { useState, useEffect } from 'react';
import { useAuthStore } from '@/lib/stores';
import { getAdminFunctions, updateAdminFunction, deleteAdminFunction, type AdminFunction } from '@/lib/api/admin';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Code, 
  Shield, 
  Search,
  Plus,
  Edit,
  Trash2,
  Download,
  Upload,
  Settings,
  Share,
  Copy,
  Play,
  Pause,
  Globe,
  Lock,
  Filter,
  MoreHorizontal,
  FileCode,
  Zap,
  AlertTriangle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export default function AdminFunctionsPage() {
  const { user, token } = useAuthStore();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFunctions, setSelectedFunctions] = useState<string[]>([]);
  const [functions, setFunctions] = useState<AdminFunction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalFunctions, setTotalFunctions] = useState(0);

  // Load functions data
  useEffect(() => {
    const loadFunctions = async () => {
      if (!token) return;
      
      try {
        setIsLoading(true);
        const response = await getAdminFunctions(token, 1, 50);
        setFunctions(response.functions);
        setTotalFunctions(response.total);
      } catch (error) {
        console.error('Failed to load functions:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadFunctions();
  }, [token]);

  // Handle function toggle
  const handleToggleFunction = async (functionId: string, currentStatus: string) => {
    if (!token) return;
    
    try {
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
      await updateAdminFunction(token, functionId, { status: newStatus as 'active' | 'inactive' | 'error' });
      setFunctions(functions.map(f => f.id === functionId ? { ...f, status: newStatus as 'active' | 'inactive' | 'error' } : f));
    } catch (error) {
      console.error('Failed to toggle function:', error);
    }
  };

  // Handle function deletion
  const handleDeleteFunction = async (functionId: string) => {
    if (!token) return;
    
    try {
      await deleteAdminFunction(token, functionId);
      setFunctions(functions.filter(f => f.id !== functionId));
      setTotalFunctions(totalFunctions - 1);
    } catch (error) {
      console.error('Failed to delete function:', error);
    }
  };

  // Filter functions based on search term
  const filteredFunctions = functions.filter(func => 
    func.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    func.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    func.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Check if user has admin permissions
  // TODO: Temporarily allowing access for debugging - restore proper admin check later
  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="p-8 text-center">
          <Shield className="w-16 h-16 mx-auto mb-4 text-red-500" />
          <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
          <p className="text-gray-600 dark:text-gray-400">
            You need to be logged in to access this page.
          </p>
          <div className="mt-4 text-sm text-gray-500">
            Current user: {user ? `${user.email} (${user.role})` : 'Not logged in'}
          </div>
        </Card>
      </div>
    );
  }

  // Helper function to format date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'builtin':
        return <Zap className="w-4 h-4 text-blue-500" />;
      case 'custom':
        return <FileCode className="w-4 h-4 text-green-500" />;
      case 'community':
        return <Globe className="w-4 h-4 text-purple-500" />;
      default:
        return <Code className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variant = status === 'active' ? 'default' : status === 'error' ? 'destructive' : 'secondary';
    return (
      <Badge variant={variant}>
        {status === 'active' ? (
          <Play className="w-3 h-3 mr-1" />
        ) : status === 'error' ? (
          <AlertTriangle className="w-3 h-3 mr-1" />
        ) : (
          <Pause className="w-3 h-3 mr-1" />
        )}
        {status}
      </Badge>
    );
  };

  const getScopeBadge = (scope: string) => {
    return (
      <Badge variant="outline">
        {scope === 'global' ? (
          <Globe className="w-3 h-3 mr-1" />
        ) : (
          <Lock className="w-3 h-3 mr-1" />
        )}
        {scope}
      </Badge>
    );
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center space-x-2">
            <Link href="/admin" className="text-gray-500 hover:text-gray-700">
              Admin
            </Link>
            <span className="text-gray-500">/</span>
            <span className="font-semibold">Functions</span>
          </div>
          <h1 className="text-3xl font-bold mt-1">Function Management</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Create, manage, and deploy custom functions and tools
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Upload className="w-4 h-4 mr-2" />
            Import
          </Button>
          <Button onClick={() => router.push('/admin/functions/create')} className="bg-[#ffffff] hover:bg-gray-100 border border-gray-200">
            <Plus className="w-4 h-4 mr-2" />
            New Function
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview">Functions Overview</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Functions</p>
                  <p className="text-2xl font-bold">{isLoading ? '...' : totalFunctions}</p>
                </div>
                <Code className="w-8 h-8 text-blue-500" />
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Functions</p>
                  <p className="text-2xl font-bold text-green-600">
                    {isLoading ? '...' : functions.filter(f => f.status === 'active').length}
                  </p>
                </div>
                <Play className="w-8 h-8 text-green-500" />
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Global Functions</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {isLoading ? '...' : functions.filter(f => f.type === 'builtin').length}
                  </p>
                </div>
                <Globe className="w-8 h-8 text-purple-500" />
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Usage</p>
                  <p className="text-2xl font-bold">
                    {functions.reduce((sum, f) => sum + f.usageCount, 0)}
                  </p>
                </div>
                <Zap className="w-8 h-8 text-orange-500" />
              </div>
            </Card>
          </div>

          {/* Search and Filters */}
          <Card className="p-6">
            <div className="flex items-center space-x-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search functions by name, description, or tags..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button variant="outline">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          </Card>

          {/* Functions Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {isLoading ? (
              <div className="col-span-full text-center p-8">
                <p className="text-gray-500">Loading functions...</p>
              </div>
            ) : filteredFunctions.length > 0 ? (
              filteredFunctions.map((func) => (
              <Card key={func.id} className="p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    {getTypeIcon(func.type)}
                    <h3 className="font-semibold text-lg">{func.name}</h3>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusBadge(func.status)}
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                  {func.description}
                </p>

                <div className="space-y-3 mb-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Version</span>
                    <Badge variant="outline">{func.version}</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Author</span>
                    <Badge variant="outline">{func.author}</Badge>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Category</span>
                    <Badge variant="outline">{func.category}</Badge>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Usage</span>
                    <span className="font-medium">{func.usage_count}x</span>
                  </div>
                </div>

                <div className="mb-4">
                  <div className="flex flex-wrap gap-1">
                    {func.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                  <span>By {func.author}</span>
                  <span>{formatDate(func.updated_at)}</span>
                </div>

                <div className="flex items-center space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="flex-1"
                    onClick={() => handleToggleFunction(func.id, func.status)}
                  >
                    {func.status === 'active' ? (
                      <>
                        <Pause className="w-3 h-3 mr-1" />
                        Disable
                      </>
                    ) : (
                      <>
                        <Play className="w-3 h-3 mr-1" />
                        Enable
                      </>
                    )}
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => router.push(`/admin/functions/edit?id=${func.id}`)}
                  >
                    <Edit className="w-3 h-3" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Copy className="w-3 h-3" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Share className="w-3 h-3" />
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="text-red-600"
                    onClick={() => handleDeleteFunction(func.id)}
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </Card>
            ))
            ) : (
              <div className="col-span-full text-center p-8">
                <Code className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-semibold mb-2">No functions found</h3>
                <p className="text-gray-500 mb-4">
                  {searchTerm ? 'No functions match your search criteria.' : 'No functions have been created yet.'}
                </p>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Function
                </Button>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          {/* Templates Header */}
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold">Function Templates</h2>
              <p className="text-gray-600 dark:text-gray-400">Start with pre-built templates</p>
            </div>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Create Template
            </Button>
          </div>

          {/* Templates Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              {
                name: 'Basic Tool Template',
                description: 'A simple template for creating custom tools',
                language: 'python',
                complexity: 'Beginner'
              },
              {
                name: 'API Integration Template', 
                description: 'Template for integrating external APIs',
                language: 'python',
                complexity: 'Intermediate'
              },
              {
                name: 'Data Processing Template',
                description: 'Template for data analysis and processing',
                language: 'python',
                complexity: 'Advanced'
              },
              {
                name: 'Web Scraper Template',
                description: 'Template for web scraping functionality',
                language: 'python',
                complexity: 'Intermediate'
              },
              {
                name: 'Database Connector Template',
                description: 'Template for database operations',
                language: 'python',
                complexity: 'Advanced'
              },
              {
                name: 'Text Processing Template',
                description: 'Template for text analysis and manipulation',
                language: 'python',
                complexity: 'Beginner'
              }
            ].map((template, index) => (
              <Card key={index} className="p-6 hover:shadow-lg transition-shadow cursor-pointer">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <FileCode className="w-5 h-5 text-blue-500" />
                    <h3 className="font-semibold">{template.name}</h3>
                  </div>
                  <Badge variant="outline" className={cn(
                    template.complexity === 'Beginner' && 'border-green-500 text-green-700',
                    template.complexity === 'Intermediate' && 'border-yellow-500 text-yellow-700',
                    template.complexity === 'Advanced' && 'border-red-500 text-red-700'
                  )}>
                    {template.complexity}
                  </Badge>
                </div>

                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  {template.description}
                </p>

                <div className="flex items-center justify-between mb-4">
                  <Badge variant="secondary">{template.language}</Badge>
                </div>

                <Button className="w-full">
                  <Plus className="w-4 h-4 mr-2" />
                  Use Template
                </Button>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}