'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuthStore } from '@/lib/stores';
import { cn } from '@/lib/utils';
import { Spinner } from '@/components/ui/spinner';
import { Leaderboard } from '@/components/admin/evaluations/Leaderboard';
import { Feedbacks } from '@/components/admin/evaluations/Feedbacks';
import { getAllFeedbacks, type Feedback as ApiFeedback } from '@/lib/api/evaluations';
import {
  BarChart,
  MessageSquare
} from 'lucide-react';




interface EvaluationTab {
  id: string;
  label: string;
  icon: React.ReactNode;
  component: React.ComponentType<any>;
}

const evaluationTabs: EvaluationTab[] = [
  {
    id: 'leaderboard',
    label: 'Leaderboard',
    icon: <BarChart className="w-4 h-4" />,
    component: Leaderboard
  },
  {
    id: 'feedbacks',
    label: 'Feedbacks',
    icon: <MessageSquare className="w-4 h-4" />,
    component: Feedbacks
  }
];

export default function AdminEvaluationsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, token } = useAuthStore();

  const [selectedTab, setSelectedTab] = useState('leaderboard');
  const [feedbacks, setFeedbacks] = useState<ApiFeedback[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    totalFeedbacks: 0,
    positiveFeedbacks: 0,
    negativeFeedbacks: 0,
    totalUsers: 0
  });

  // Get tab from URL params
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && ['leaderboard', 'feedbacks'].includes(tab)) {
      setSelectedTab(tab);
    }
  }, [searchParams]);

  // Load data
  useEffect(() => {
    if (user && token) {
      loadFeedbacks();
    }
  }, [user, token]);

  const loadFeedbacks = async () => {
    if (!token) return;

    setIsLoading(true);
    try {
      const feedbacksData = await getAllFeedbacks(token);
      setFeedbacks(feedbacksData);

      // Calculate stats
      const totalFeedbacks = feedbacksData.length;
      const positiveFeedbacks = feedbacksData.filter(f => f.data.rating >= 4).length;
      const negativeFeedbacks = feedbacksData.filter(f => f.data.rating <= 2).length;
      const totalUsers = new Set(feedbacksData.map(f => f.user_id)).size;

      setStats({
        totalFeedbacks,
        positiveFeedbacks,
        negativeFeedbacks,
        totalUsers
      });
    } catch (error) {
      console.error('Failed to load feedbacks:', error);
      // Set empty state on error
      setFeedbacks([]);
      setStats({
        totalFeedbacks: 0,
        positiveFeedbacks: 0,
        negativeFeedbacks: 0,
        totalUsers: 0
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTabChange = (tabId: string) => {
    setSelectedTab(tabId);
    router.push(`/admin/evaluations?tab=${tabId}`);
  };

  const SelectedComponent = evaluationTabs.find(tab => tab.id === selectedTab)?.component;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="flex flex-col lg:flex-row w-full h-full pb-2 lg:space-x-4">
      {/* Sidebar Navigation */}
      <div className="tabs flex flex-row overflow-x-auto gap-2.5 max-w-full lg:gap-1 lg:flex-col lg:flex-none lg:w-40 dark:text-gray-200 text-sm font-medium text-left scrollbar-none">
        {evaluationTabs.map((tab) => (
          <button
            key={tab.id}
            className={cn(
              "px-0.5 py-1 min-w-fit rounded-lg lg:flex-none flex text-right transition",
              selectedTab === tab.id
                ? "text-gray-900 dark:text-white"
                : "text-gray-300 dark:text-gray-600 hover:text-gray-700 dark:hover:text-white"
            )}
            onClick={() => handleTabChange(tab.id)}
          >
            <div className="flex items-center gap-2">
              {tab.icon}
              <span>{tab.label}</span>
            </div>
          </button>
        ))}
      </div>

      {/* Content Area */}
      <div className="flex-1 mt-1 lg:mt-0 overflow-y-scroll">
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 h-full">
          {SelectedComponent ? (
            <SelectedComponent feedbacks={feedbacks} stats={stats} />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <BarChart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  Evaluation Panel
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  Select a tab to view evaluation data
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
