'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuthStore } from '@/lib/stores';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Menu } from 'lucide-react';
import { cn } from '@/lib/utils';

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user } = useAuthStore();
  const router = useRouter();
  const pathname = usePathname();
  const [loaded, setLoaded] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);

  useEffect(() => {
    if (user?.role !== 'admin') {
      router.push('/');
      return;
    }
    setLoaded(true);
  }, [user, router]);

  if (!loaded) {
    return null;
  }

  const adminTabs = [
    { name: 'Users', href: '/admin/users', active: pathname.includes('/admin/users') || pathname === '/admin' },
    { name: 'Evaluations', href: '/admin/evaluations', active: pathname.includes('/admin/evaluations') },
    { name: 'Functions', href: '/admin/functions', active: pathname.includes('/admin/functions') },
    { name: 'Settings', href: '/admin/settings', active: pathname.includes('/admin/settings') },
  ];

  return (
    <div className={cn(
      "flex flex-col w-full h-screen max-h-[100dvh] transition-width duration-200 ease-in-out",
      // showSidebar ? "md:max-w-[calc(100%-260px)]" : "",
      "max-w-full min-w-0"
    )}>
      {/* Navigation Header */}
      <nav className="px-2.5 pt-1 backdrop-blur-xl drag-region">
        <div className="flex items-center gap-1">
          <div className={cn(showSidebar ? "md:hidden" : "", "flex flex-none items-center self-end")}>
            <Button
              variant="ghost"
              size="sm"
              className="cursor-pointer p-1.5 flex rounded-xl hover:bg-gray-100 dark:hover:bg-gray-850 transition"
              onClick={() => setShowSidebar(!showSidebar)}
              aria-label="Toggle Sidebar"
            >
              <Menu className="w-4 h-4" />
            </Button>
          </div>

          <div className="flex w-full">
            <div className="flex gap-1 scrollbar-none overflow-x-auto w-fit text-center text-sm font-medium rounded-full bg-transparent pt-1">
              {adminTabs.map((tab) => (
                <Link
                  key={tab.name}
                  href={tab.href}
                  className={cn(
                    "min-w-fit rounded-full p-1.5 transition",
                    tab.active
                      ? ""
                      : "text-gray-300 dark:text-gray-600 hover:text-gray-700 dark:hover:text-white"
                  )}
                >
                  {tab.name}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </nav>

      {/* Content Area */}
      <div className="pb-1 px-[16px] flex-1 max-h-full overflow-y-auto">
        {children}
      </div>
    </div>
  );
}