"use client";

import React, { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { AppLayout } from "@/components/layout/AppLayout";
import { Chat } from "@/components/chat";
import { Agent } from "@/components/agent/Agent";
import { useAppStore } from "@/lib/stores";
import { use } from "react";
import type { ChatPageProps } from "@/lib/types/page";

function ChatPageContent({ chatId }: { chatId: string }) {
  const { selectedModels, setSelectedModels } = useAppStore();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedMode, setSelectedMode] = useState<'chat' | 'agent'>('chat');

  // Get mode from URL params or default to 'chat'
  useEffect(() => {
    const mode = searchParams.get('mode') as 'chat' | 'agent';
    if (mode && (mode === 'chat' || mode === 'agent')) {
      setSelectedMode(mode);
    }
  }, [searchParams]);

  // Update URL when mode changes
  const handleModeChange = (mode: 'chat' | 'agent') => {
    setSelectedMode(mode);
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('mode', mode);
    router.replace(currentUrl.pathname + currentUrl.search);
  };

  const handleModelsChange = (models: string[]) => {
    setSelectedModels(models);
  };

  // Validate chatId format (basic check)
  if (!chatId || chatId.trim() === '') {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Invalid Chat ID
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            The chat ID provided is not valid.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Content Area */}
      <div className="flex-1 overflow-hidden h-full">
        {selectedMode === 'chat' ? (
          <Chat
            chatId={chatId}
            selectedModels={selectedModels}
            onModelsChange={handleModelsChange}
          />
        ) : (
          <Agent
            chatId={chatId}
            selectedModels={selectedModels}
          />
        )}
      </div>
    </div>
  );
}

export default function ChatPage({ params }: ChatPageProps) {
  const { chatId } = use(params);

  return (
    <AppLayout>
      <Suspense fallback={<div className="flex items-center justify-center h-full">Loading...</div>}>
        <ChatPageContent chatId={chatId} />
      </Suspense>
    </AppLayout>
  );
}